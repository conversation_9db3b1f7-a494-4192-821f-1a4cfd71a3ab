#======================================#
#========== Project settings ==========#
#======================================#

# 产品或项目名称、软件开发公司名称
productName: 住房保障与办公用房管理系统
companyName: ThinkGem

# 产品版本、版权年份
productVersion: V5.9
copyrightYear: 2024

# 是否演示模式
demoMode: false

# 专为分离端提供接口服务
apiMode: false

#======================================#
#========== Server settings ===========#
#======================================#
sys:
  index:
    menuStyle: 2
server:
  port: 8980
  servlet:
    context-path: /hsobs
    register-default-servlet: false

  #    encoding.enabled: true
  max-http-header-size: 8192
  tomcat:
    redirect-context-root: false
    uri-encoding: UTF-8
    # 表单请求数据的最大大小
    max-http-form-post-size: 20MB
    connection-timeout: 8000
    keep-alive-timeout: 20000
    max-keep-alive-requests: 100
    # 慢请求主动中断 (Tomcat 9.x特有防御)

    #    # 进程的最大连接数
    max-connections: 10000
    #    # 连接数满后的排队个数
    accept-count: 100
    #    # 线程数最大和最小个数
    threads:
      max: 200
      min-spare: 10

  # 当 Nginx 为 https，tomcat 为 http 时，设置该选项为 true
  schemeHttps: false

#======================================#
#========== Database sttings ==========#
#======================================#

# 数据库连接
jdbc:
  # Mysql 数据库配置
  #  type: mysql
  #  driver: com.mysql.cj.jdbc.Driver
  #  url: ********************************************************************************************************************************************************************************************
  #  username: root
  #  password: TxdHP2SeUyT5
  #  testSql: SELECT 1

  type: dameng
  driver: dm.jdbc.driver.DmDriver
  #  url: jdbc:dm://106.227.74.200:30236?schema=HSOBSN&compatibleMode=mysql&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
  url: jdbc:dm://106.227.74.200:30236?schema=HSOBSG&compatibleMode=mysql&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
#  url: jdbc:dm://106.227.74.200:30236?schema=HSOBSB&compatibleMode=mysql&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
  username: HSOBSN
  password: v8yjYl9J5TVB
  testSql: SELECT 1

  #  # Oracle 数据库配置（11g，若使用 12c 以上版本，请打开 /modules/core/pom.xml 文件，替换为 Oracle 12c 驱动并编译打包 core 模块）
  #  type: oracle
  #  driver: oracle.jdbc.OracleDriver
  #  url: *************************************
  #  username: jeesite
  #  password: jeesite
  #  testSql: SELECT 1 FROM DUAL

  #  # Sql Server 数据库配置（2008 版本，请打开 /modules/core/pom.xml 文件，替换为 SqlServer 2008 驱动并编译打包 core 模块）
  #  type: mssql
  #  driver: net.sourceforge.jtds.jdbc.Driver
  #  url: ********************************************
  #  username: jeesite
  #  password: jeesite
  #  testSql: SELECT 1

  #  # Sql Server 数据库配置（2012 及以上版本，请打开 /modules/core/pom.xml 文件，替换为 SqlServer 2021 驱动并编译打包 core 模块）
  #  type: mssql2012
  #  driver: com.microsoft.sqlserver.jdbc.SQLServerDriver
  #  url: *********************************************************************************************
  #  username: jeesite
  #  password: jeesite
  #  testSql: SELECT 1

  #  # PostgreSql 数据库配置
  #  type: postgresql
  #  driver: org.postgresql.Driver
  #  url: ****************************************
  #  username: jeesite
  #  password: jeesite
  #  testSql: SELECT 1

  #  # H2 数据库配置（请打开 /modules/core/pom.xml 文件，打开 H2 DB 驱动并编译打包 core 模块）
  #  type: h2
  #  driver: org.h2.Driver
  #  url: jdbc:h2:~/jeesite-db/jeesite
  #  username: jeesite
  #  password: jeesite
  #  testSql: SELECT 1

  # 连接信息加密
  encrypt:
    # 加密连接用户名
    username: false
    # 加密连接密码
    password: true

  # 数据库连接池配置
  pool:
    # 初始化连接数
    init: 1
    # 最小空闲连接数
    minIdle: 3
    # 最大激活连接数
    maxActive: 20

#    # 连接超时参数，单位毫秒 （v5.5.2+）
#    connectTimeout: ~
#    socketTimeout: ~
#
#    # 查询超时时间，事务超时时间 （v5.7.1+）
#    queryTimeout: ~
#    transactionQueryTimeout: ~
#
#    # 获取连接等待超时时间，单位毫秒（1分钟）（4.0.6+）
#    maxWait: 60000
#
#    # 连接失败后中断，默认为 false 时，会一直尝试连接，为 true 时，自动中断尝试（v5.9.0+）
#    breakAfterAcquireFailure: false
#
#    # 从池中取出和归还连接前进行检验，如果检验失败，则从池中去除连接并尝试取出另一个（4.0.6+）
#    testOnBorrow: false
#    testOnReturn: false
#
#    # 间隔多久才进行一次检测，检测需要关闭的空闲连接，单位毫秒（1分钟）（4.0.6+）
#    timeBetweenEvictionRunsMillis: 60000
#
#    # 一个连接在池中最小空闲的时间，单位毫秒（20分钟）（4.0.6+）
#    minEvictableIdleTimeMillis: 1200000
#    # 一个连接在池中最大空闲的时间，单位毫秒（30分钟）（4.1.2+）
#    maxEvictableIdleTimeMillis: 1800000
#
#    # 连接池中的minIdle数量以内的连接，空闲时间超过minEvictableIdleTimeMillis，则会执行keepAlive操作（4.1.8+）
#    keepAlive: false
#
#    # 是否自动回收泄露的连接和超时时间，单位秒（35分钟）（4.0.6+）
#    removeAbandoned: false
#    removeAbandonedTimeout: 2100
#
#    # 是否缓存 PreparedStatement 对象的最大数量（4.1.5+）
#    maxPoolPreparedStatementPerConnectionSize: ~
#
#    # 设置连接属性，可获取到表的 remark (备注)
#    remarksReporting: false

#  # 读写分离配置（专业版）v4.3.0
#  readwriteSplitting:
#    # 读库的数据源名称列表（默认数据源）
#    readDataSourceNames: ds_read_01, ds_read_02
#    # 负载均衡算法（ROUND_ROBIN轮询、RANDOM随机、自定义类名）
#    loadBalancerAlgorithm: RANDOM
#
#  # 多数据源名称列表，多个用逗号隔开，使用方法：@MyBatisDao(dataSourceName="ds2")
#  dataSourceNames: ds_read_01, ds_read_02
#
#  # 默认数据源的从库01
#  ds_read_01:
#    type: mysql
#    driver: com.mysql.cj.jdbc.Driver
#    url: ***************************************************************************************************************************************************************
#    username: root
#    password: 123456
#    testSql: SELECT 1
#    pool:
#      init: 1
#      minIdle: 3
#      maxActive: 20
#
#  # 默认数据源的从库02
#  ds_read_02:
#    type: mysql
#    driver: com.mysql.cj.jdbc.Driver
#    url: ****************************************************************************************************************************************************************
#    username: root
#    password: 123456
#    testSql: SELECT 1
#    pool:
#      init: 1
#      minIdle: 3
#      maxActive: 20

#  # 多数据源名称列表，多个用逗号隔开，使用方法：@MyBatisDao(dataSourceName="ds2")
#  dataSourceNames: ds2
#
#  # 多数据源配置：ds2
#  ds2:
#    type: mysql
#    driver: com.mysql.cj.jdbc.Driver
#    url: ***********************************************************************************************************************************************************
#    username: root
#    password: 123456
#    testSql: SELECT 1
#    # 其它数据源支持密码加密
#    encrypt:
#      username: false
#      password: true
#    # 其它数据源支持连接池设置
#    pool:
#      init: 1
#      minIdle: 3
#      maxActive: 20
#    # 其它数据源支持读写分离
#    readwriteSplitting:
#      readDataSourceNames: ~
#      loadBalancerAlgorithm: RANDOM

#  # 数据源映射（Dao类名 = 数据源名称），优先于 @MyBatisDao(dataSourceName="ds2") 设置 v4.3.0
#  # Dao类名，不仅支持某个具体 Dao类名，还支持 Dao 里的某个方法指定数据源名称，还支持包路径指定数据源等
#  # 数据源名指定 {dynamic} 时支持动态，相当于 @MyBatisDao(dataSourceName=DataSourceHolder.DYNAMIC)
#  # 数据源支持指定变量 {corpCode}、 {userCode}、{userCache中的Key名}、{yml或sys_config中的Key名}
#  # 从上到下，先匹配先受用规则，默认数据源名为 default 扩展数据源为 dataSourceNames 列表里自定义的名字
#  mybatisDaoAndDataSourceMappings: |
#    TestDataChildDao = ds2
#    EmpUserDao.findList = ds2
#    com.jeesite.modules.sys. = default
#    com.jeesite.modules.filemanager. = ds2

#  # JTA XA 事务（v4.0.4+）
#  jta:
#    enabled: false

#  注意：如果报  oracle.jdbc.xa.OracleXAResource.recover 错误，则需要授权如下：
#  grant select on sys.dba_pending_transactions to jeesite;
#  grant select on sys.pending_trans$ to jeesite;
#  grant select on sys.dba_2pc_pending to jeesite;
#  grant execute on sys.dbms_system to jeesite;

#  # 事务超时时间，单位秒（30分钟）（v4.1.5+）
#  transactionTimeout: 1800
#
#  # 表名和字段名（前缀|后缀）是否强制大写（v4.1.8+）
#  tableAndColumn:
#    prefixSuffix: "`|`"
#    forceUpperCase: true
#
#  # 表名前缀
#  tablePrefix: js_

#======================================#
#========== Spring settings ===========#
#======================================#
#======================================#
spring:
  # 应用程序名称
  application:
    name: hsobs-early

  # 环境名称（注意：不可设置为 test 它是单元测试专用的名称）
  profiles:
    active: default
    # 引入外部API接口配置
    include: external-api

  # 打印横幅
  main:
    bannerMode: "off"

  # MVC 映射匹配策略
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

#  # Redis 连接参数 （RedisProperties）
#  redis:
#    host: 127.0.0.1
#    port: 6379
#    ssl: false
#    database: 0
#    password: 1234
#    timeout: 20000
#    lettuce:
#      pool:
#        # 最大空闲连接数
#        maxIdle: 3
#        # 最大活动连接数
#        maxActive: 20

#  # 缓存配置
#  cache:
#    # 缓存及会话共享（专业版）
#    isClusterMode: true
#    # 给缓存Key增加数据源名称前缀
#    keyPrefixWithDsName: false
#    # 清理全部缓存按钮所清理的缓存列表
#    clearNames: sysCache,corpCache,userCache,roleCache,fileUploadCache,msgPcPoolCache,cmsCache,bpmFormCache
#    # 用户缓存
#    userCache:
#      clearTaskPool:
#        corePoolSize: 5
#        maxPoolSize: 20
#        keepAliveSeconds: 60

# 日志配置
logging:
  config: classpath:config/logback-spring.xml

#======================================#
#========== System settings ===========#
#======================================#

# 管理基础路径
adminPath: /admin

# 前端基础路径
#frontPath: /f

# 加密设置
#encrypt:
#  # 默认秘钥，可通过 AesUtils.genKeyString() 生成新秘钥 Hex 编码
#  defaultKey: 9f58a20946b47e190003ec716c1c457d
#  # 是否使用国密 SM 算法（SHA-1 替换为 SM3、AES 替换为 SM4）
#  smAlgorithm: false
#  # 对称或非对称加密是否使用 Base64 存储，默认 Hex 存储
#  storeBase64: false

# 分页相关
#page:
#
#  # 默认每页显示的数据条数
#  pageSize: 20
#
#  # 每页最大条数，防止分页过大导致系统缓慢或内存溢出
#  maxPageSize: 999

# 用户相关
user:
  enabled: true
  #  # 指定超级管理员编号（研发团队使用的账号）
  #  superAdminCode: system
  #
  #  # 超级管理员获取菜单的最小权重（默认20；>=40二级管理员；>=60系统管理员；>=80超级管理员）
  #  superAdminGetMenuMinWeight: 40
  #
  #  # 系统管理员角色编号（客户方管理员使用的角色）
  #  corpAdminRoleCode: corpAdmin
  #
  #  # 二级管理员的控制权限类型（1拥有的权限 2管理的权限，管理功能包括：用户管理、组织机构、公司管理等）（v4.1.5+）
  adminCtrlPermi: 1
  # 多租户模式（SAAS模式）（专业版）
  useCorpModel: false

#  # 登录账号是否租户内唯一，否则全局唯一
#  loginCodeCorpUnique: false
#
#  # 是否启用验证码登录（手机、邮箱）
#  loginByValidCode: true
#
#  # 用户类型配置信息（employee员工，member会员，btype往来单位，persion个人，expert专家，...），JSON 格式说明如下：
#  # {"用户类型":{"beanName":"Service或Dao的Bean名称","loginView":"登录页面视图","indexView":"主框架页面视图，支持 redirect: 前缀"}}
#  userTypeMap: >
#    {
#      employee: {beanName: "employeeService", loginView: "", indexView: "modules/sys/sysIndex"},
#      member: {beanName: "memberService", loginView: "", indexView: "modules/sys/sysIndexMember"},
#      btype: {beanName: "btypeInfoService", loginView: "", indexView: "modules/sys/sysIndexBtype"},
#      expert: {beanName: "expertService", loginView: "", indexView: "modules/sys/sysIndexExpert"}
#    }
#
#  # 数据权限设置参数，可新增自定义数据权限，moduleCode: 针对模块, ctrlPermi: 权限类型, 0全部  1拥有权限  2管理权限
#  dataScopes: >
#    [{
#      moduleCode: "core",
#      ctrlPermi: "0",
#      ctrlName: "机构权限",
#      ctrlName_en: "Office",
#      ctrlType: "Office",
#      ctrlDataUrl: "/sys/office/treeData",
#      chkboxType: {"Y":"ps","N":"ps"},
#      expandLevel: -1,
#      remarks: ""
#    },{
#      moduleCode: "core",
#      ctrlName: "公司权限",
#      ctrlName_en: "Company",
#      ctrlType: "Company",
#      ctrlPermi: "0",
#      ctrlDataUrl: "/sys/company/treeData",
#      chkboxType: {"Y":"ps","N":"ps"},
#      expandLevel: -1,
#      remarks: ""
#    },{
#      moduleCode: "core",
#      ctrlName: "角色权限",
#      ctrlName_en: "Role",
#      ctrlType: "Role",
#      ctrlPermi: "2",
#      ctrlDataUrl: "/sys/role/treeData",
#      chkboxType: {"Y":"ps","N":"ps"},
#      expandLevel: -1,
#      remarks: ""
#    }]
#
#  # 数据权限调试模式（会输出一些日志）
#  dataScopeDebug: false
#
#  # 数据权限使用 API 方式实现（适应 Cloud 环境，基础用户表与业务数据表跨库的情况）
#  # 开启后设置 ctrlDataAttrName 加 AndChildren 后缀，ctrlDataParentCodesAttrName 清空
#  # 以方便读取树结构数据权限的表时包含子节点，举例如下：
#  # ctrlDataAttrName: "officeCodesAndChildren", ctrlDataParentCodesAttrName: ""
#  dataScopeApiMode: false

# 角色管理
#role:
#  # 扩展数据权限定义：3：本部门；4：本公司；5：本部门和本公司
#  extendDataScopes: >
#    {
#      3: {
#        Office: {
#          #控制类型的类名 : "用来获取控制表名和主键字段名，如果为 NONE，则代表是不控制该类型权限",
#          ctrlTypeClass: "com.jeesite.modules.sys.entity.Office",
#          #控制数据的类名: "指定一个静态类名，方便 ctrlDataAttrName 得到权限数据，如：当前机构编码、当前公司编码、当前行业编码等",
#          ctrlDataClass: "com.jeesite.modules.sys.utils.EmpUtils",
#          #控制数据的类名下的属性名 : "可看做 ctrlDataClass 下的 get 方法，如：EmpUtils.getOfficeCodes()，支持返回字符串或字符串数组类型",
#          ctrlDataAttrName: "officeCodes",
#          #控制数据的所有上级编码 : "用于控制数据为树表的情况，为数组时，必须与 ctrlDataAttrName 返回的长度相同，不是树表设置为空",
#          ctrlDataParentCodesAttrName: "officeParentCodess"
#        },
#        Company: {
#          ctrlTypeClass: "NONE"
#        }
#      },
#      4: {
#        Office: {
#          ctrlTypeClass: "NONE"
#        },
#        Company: {
#          ctrlTypeClass: "com.jeesite.modules.sys.entity.Company",
#          ctrlDataClass: "com.jeesite.modules.sys.utils.EmpUtils",
#          ctrlDataAttrName: "company.companyCode",
#          ctrlDataParentCodesAttrName: "company.parentCodes"
#        }
#      },
#      5: {
#        Office: {
#          ctrlTypeClass: "com.jeesite.modules.sys.entity.Office",
#          ctrlDataClass: "com.jeesite.modules.sys.utils.EmpUtils",
#          ctrlDataAttrName: "officeCodes",
#          ctrlDataParentCodesAttrName: "officeParentCodess"
#        },
#        Company: {
#          ctrlTypeClass: "com.jeesite.modules.sys.entity.Company",
#          ctrlDataClass: "com.jeesite.modules.sys.utils.EmpUtils",
#          ctrlDataAttrName: "company.companyCode",
#          ctrlDataParentCodesAttrName: "company.parentCodes"
#        }
#      }
#    }

# 菜单管理
#menu:
#  # 根据模块状态去更新相连的菜单状态
#  updateStatusByModuleStatus: false

# 国际化管理（专业版）
lang:
  enabled: true

#  # 默认语言（4.1.3+）
#  defaultLocale: zh_CN
#  # 默认时区（4.1.3+）
#  defaultTimeZone: GMT+08:00

# 任务调度（标准版）
job:
  enabled: true

  #  # 是否自动启动任务调度（可关闭）
  #  autoStartup: true
  #
  #  # 任务调度启动延迟设置（单位：秒）（建议设置项目启动完成后的时间）
  #  startupDelay: 60
  #
  #  # 任务调度线程池
  #  threadPool:
  #    threadCount: 10
  #    threadPriority: 5
  #
  #  # 调度设置，集群中每一个实例都必须使用相同的instanceName名称 （区分特定的调度器实例）
  #  # 在微服务模式情况下，请将instanceName名称设置为当前微服务的名称
  #  # 每一个instanceId必须不同，设置AUTO则自动生成
  scheduler:
    #    instanceName: JeeSiteScheduler
    instanceName: ${spring.application.name}
    instanceId: AUTO
#
#  # 任务调度集群设置
#  jobStore:
#    isClustered: true
#    dataSourceName: job
#    clusterCheckinInterval: 1000
#    className: org.springframework.scheduling.quartz.LocalDataSourceJobStore
#    misfireThreshold: 1000

#  # 调度日志
#  log:
#    # 计划调度日志
#    scheduler:
#      enabled: true
#      # 是否只保存错误日志
#      errorLevel: true
#    # 任务执行日志
#    jobDetail:
#      enabled: true
#      # 是否只保存错误日志
#      errorLevel: true
#    # 计划触发日志
#    trigger:
#      enabled: false

# 代码生成
gen:
  enabled: true

#  # 表名字段名是否强制小写
#  forceLowerCase: true

# 系统监控
state:
  enabled: false

#======================================#
#========= Framework settings =========#
#======================================#
#hsobs:
#  sso:
#    # 统一认证回调地址
#    redirectUrl: http://127.0.0.1:8980
#    # 统一认证回调路径
#    redirectPath: ${hsobs.sso.redirectUrlredirectUrl}/sso/login
#    # 统一认证请求地址-认证服务端
#    loginUrl: http://127.0.0.1:8980
#    # 廷议认证请求路径-认证服务端
#    loginPath: ${hsobs.sso.loginUrl}/digit-space-sso/p3/serviceValidate?service=${hsobs.sso.redirectPath}

# Shiro 相关
shiro:
  # 主页路径
  defaultPath: ${shiro.loginUrl}

  # 登录相关设置
  loginUrl: ${adminPath}/login
  logoutUrl: ${shiro.loginUrl}
  successUrl: ${adminPath}/index

  # 是否开启线上环境 true-是  false-否
  onlineFlag: false
  # 私网登录环境配置
  defaultPriPath: ${shiro.loginPriUrl}
  loginPriUrl: ${adminPath}/login
  logoutPriUrl: ${shiro.loginUrl}

  defaultPubPath: ${shiro.loginPubUrl}
  loginPubUrl: ${adminPath}/login
  logoutPubUrl: ${shiro.loginUrl}

  #  # Apereo CAS 相关配置（标准版）
  casServerUrl: http://127.0.0.1:8980/hsobs/digit-space-sso/p3/serviceValidate
  # 互联网环境
  casServerPublicUrl: http://127.0.0.1:8980/hsobs/digit-space-sso/p3/serviceValidate
  # 是否公网请求判断条件  Host
  casServerPublicHostHeader: jgswglj.fujian.gov.cn
  casClientUrl: http://127.0.0.1:8980/hsobs
  #  loginUrl: ${shiro.casServerUrl}?service=${shiro.casClientUrl}/sso/login
  #  logoutUrl: ${shiro.casServerUrl}/logout?service=${shiro.loginUrl}
  #  successUrl: ${shiro.casClientUrl}${adminPath}/index

  #  # LDAP 相关设置（标准版）
  #  ldapUrl: ldap://127.0.0.1:389
  #  ldapUserDn: uid={0},ou=users,dc=mycompany,dc=com

  # 简单 SSO 登录相关配置
  sso:
    # 如果启用/sso/{username}/{token}单点登录，请修改此安全key并与单点登录系统key一致。
    secretKey: kRh#1g5ssQ
    # 是否加密单点登录安全Key
    encryptKey: true
    # token 时效性，如：1天：yyyyMMdd、1小时：yyyyMMddHH、1分钟：yyyyMMddHHmm
    encryptKeyDateFormat: yyyyMMddHH

  # 登录提交信息加密（如果不需要加密，设置为空即可）
  loginSubmit:
    # 加密用户名、密码、验证码，后再提交（key设置为3个，用逗号分隔）加密方式：DES（4.1.9及之前版本默认设置）
    # v4.2.0+ 开始支持 Base64 加密方式，方便移动端及第三方系统处理认证，可直接设置 Key 为 Base64（4.2.0+默认设置）
    #secretKey: thinkgem,jeesite,com
    secretKey: kRh#1g5ssQ
    #secretKey: ~

  # 记住我密钥设置，你可以通过 com.jeesite.test.RememberMeKeyGen 类快速生成一个秘钥。
  # 若不设置，则每次启动系统后自动生成一个新秘钥，这样会导致每次重启后，客户端记录的用户信息将失效。
  rememberMe:
    secretKey: ~

  #  # 指定获取客户端IP的Header名称，防止IP伪造。指定为空，则使用原生方法获取IP。
  #  remoteAddrHeaderName: X-Forwarded-For
  #
  #  # 允许的请求方法设定，解决安全审计问题（BPM设计器用到了PUT或DELETE方法）
  #  allowRequestMethods: GET, POST, OPTIONS, PUT, DELETE
  #
  #  # 是否允许账号多地登录，如果设置为false，同一个设备类型的其它地点登录的相同账号被踢下线
  #  isAllowMultiAddrLogin: true
  #
  #  # 是否允许多账号多设备登录，如果设置为false，其它地点登录的相同账号全部登录设备将被踢下线
  #  isAllowMultiDeviceLogin: true
  #
  #  # 是否允许刷新主框架页，如果设置为false，刷新主页将导致重新登录。如安全性比较高的，如银行个人首页不允许刷新。
  #  isAllowRefreshIndex: true
  #
  #  # 是否允许嵌入到外部网站iframe中（true：不限制，false：不允许）
  #  isAllowExternalSiteIframe: true
  #
  #  # 设定允许获取的资源列表（v4.2.3）
  #  #contentSecurityPolicy: "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; img-src 'self' 'unsafe-inline' 'unsafe-eval' data:"

  #  # 是否允许跨域访问 CORS，如果允许，设置允许的域名。v4.2.3 开始支持多个域名和模糊匹配，例如：http://*.jeesite.com,http://*.jeesite.net
  #  accessControlAllowOrigin: '*'
  #
  #  # 允许跨域访问时 CORS，可以获取和返回的方法和请求头
  #  accessControlAllowMethods: GET, POST, OPTIONS
  #  accessControlAllowHeaders: content-type, x-requested-with, x-ajax, x-token, x-remember
  #  accessControlExposeHeaders: x-token, x-remember

  #  # 是否允许接收跨域的Cookie凭证数据 CORS
  #  accessControlAllowCredentials: false
  #
  #  # 允许的网站来源地址，不设置为全部地址（避免一些跨站点请求伪造 CSRF、防盗链）
  #  allowReferers: http://127.0.0.1,http://localhost
  #
  #  # 允许重定向的地址，不设置为全部允许，设置this只允许本项目内部跳转，多个用逗号隔开，例如：this,http://*.jeesite.com
  #  allowRedirects: ~
  #  allowRedirects: this
  #
  #  # 是否在登录后生成新的Session（默认false）
  #  isGenerateNewSessionAfterLogin: false
  #
  #  # 内部系统访问过滤器，可设置多个允许的内部系统IP地址串，多个用逗号隔开，完整的IP使用“]”符号结尾
  #  innerFilterAllowRemoteAddrs: 127.0.0.1]

  #    /tags/* = anon
  #    /lang/** = anon
  #    /account/* = anon
  #    /userfiles/** = anon
  #    /validCode = anon
  #    /static/** = anon
  #    /oauth2/login/** = anon
  #    /oauth2/binder/** = anon
  #    /oauth2/callback/** = anon
  # URI 权限过滤器定义（自定义添加参数时，请不要移除 ${adminPath}/** = user，否则会导致权限异常）
  # 提示：填写过滤规则，请注意先后顺序，从上到下，先匹配先受用规则，匹配成功后不再继续匹配。
  filterChainDefinitions: |
    /tags/** = user
    /oauth2/authorize = user
    /static/modules/msg/** = user
    /f/** = user
    /druid/** = perms[sys:state:druid]
    /bpm/modeler/** = perms[bpm:modeler]
    ${adminPath}/** = user
    /api/** = anon
    /ssoJson/** = anon
    /sso/login = anon
    /** = user

#  # URI 权限过滤器定义（以下参考，必须登录user可访问的地址和不需要登录anon可访问地址）
#  filterChainDefinitions: |
#    /ReportServer/** = user
#    ${adminPath}/file/** = anon
#    ${adminPath}/cms/* = anon
#    ${adminPath}/cms/site/select = anon
#    ${adminPath}/cms/site/* = anon
#    ${adminPath}/cms/category/treeData = anon
#    ${adminPath}/cms/category/* = anon
#    ${adminPath}/cms/article/* = anon
#    ${adminPath}/cms/link/* = anon
#    ${adminPath}/sys/corpAdmin/treeData = anon
#    ${adminPath}/${spring.application.name}/swagger/** = anon
#    ${adminPath}/** = user

# Session 相关
session:
  # 会话超时时间，单位：毫秒，10m=600000, 20m=1200000ms, 30m=1800000ms, 60m=3600000ms, 12h=43200000ms, 1day=86400000ms
  # 注意：如果超时超过30m，你还需要同步修改当前配置文件的属性：j2cache.caffeine.region.sessionCache 超时时间，大于这个值。

  # 游客会话超时时间：只访问了系统，但未登录系统的用户为游客，游客默认超时时间为3分钟，如：未登录系统时的图片验证码有效时间。
  sessionTimeout: 180000

  # 登录系统后的用户超时时间（不明确 param_deviceType 参数的，默认设备为 pc 登录）
  pcSessionTimeout: 1800000

#  # 手机APP设备会话超时参数设置，登录请求参数加 param_deviceType=mobileApp 时有效，一般情况无需设置
#  mobileAppSessionTimeout: 1800000
#
#  # 定时清理失效会话，清理用户直接关闭浏览器造成的孤立会话（设置为 0 关闭，微服务下只开启 core 即可）
#  sessionTimeoutClean: 1200000
#
#  # 会话唯一标识SessionId在Cookie中的名称。
#  sessionIdCookieName: jeesite.session.id
#  #sessionIdCookiePath: ${server.servlet.context-path}
#
#  # 共享的SessionId的Cookie名称，保存到跟路径下，第三方应用获取。同一域名下多个项目时需设置共享Cookie的名称。
#  shareSessionIdCookieName: ${session.sessionIdCookieName}
#
#  # 其它 SimpleCookie 参数（v4.2.3）
#  sessionIdCookieSecure: false
#  sessionIdCookieHttpOnly: true
#  sessionIdCookieSameSite: LAX
#
#  # 设置接收 SessionId 请求参数和请求头的名称
#  sessionIdParamName: __sid
#  sessionIdHeaderName: x-token
#
#  # 当直接通过 __sid 参数浏览器访问页面时，可将直接将 __sid 写入 Cookie 应用于后面的访问
#  # 访问地址举例：http://host/js/a/index?__sid=123456&__cookie=true
#  writeCookieParamName: __cookie
#
#  # 记住我的请求参数和请求头的名称（v4.2.3）
#  rememberMeHeaderName: x-remember

# 系统缓存配置
#j2cache:
#
#  # 一级缓存
#  caffeine:
#    region:
#      #[cacheName]: size, xxxx[s|m|h|d]
#      default: 10000, 1h
#      sessionCache: 100000, 12h
#
#  # 二级缓存
#  redis:
#    # 存储模式 （generic|hash）
#    storage: hash
#    # 通知订阅的通道名
#    channel: jeesite
#    # 缓存命名空间名
#    namespace: jeesite
#    # 消息监听器
#    listener:
#      taskPool:
#        corePoolSize: 8
#        maxPoolSize: 20
#        keepAliveSeconds: 60
#        queueCapacity: 1000
#
#  # 通知订阅
#  broadcast:
#    # 缓存清理模式
#    # passive -> 被动清除，一级缓存过期进行通知各节点清除一二级缓存
#    # active -> 主动清除，二级缓存过期主动通知各节点清除，优点在于所有节点可以同时收到缓存清除，存储模式需要设置为 generic
#    # blend -> 两种模式一起运作，对于各个节点缓存准确以及及时性要求高的可以使用，正常用前两种模式中一个就可
#    cache_clean_mode: passive

# MyBatis 相关
mybatis:
  # @MyBatisDao 扫描基础包，如果多个，用“,”分隔
  scanBasePackage: com.jeesite.modules.**.dao,com.hsobs.**.dao,

  # TypeAliases 扫描基础包，如果多个，用“,”分隔 v5.3.1
  scanTypeAliasesBasePackage: com.jeesite.modules.**.entity,com.hsobs.**.entity

  #  # TypeHandlers 扫描基础包，如果多个，用“,”分隔
  #  scanTypeHandlersPackage: ~
  #
  #  # 自定义 Mapper 文件扫描路径，如果多个，用“,”分隔 v4.2.3
  #  scanMapperLocations: classpath*:/mappings/**/*.xml
  #
  #  # 是否开启 JDBC 管理事务，默认 Spring 管理事务 v4.2.3
  #  jdbcTransaction: false
  #
  #  # 批量插入和更新的分批默认大小（防止库一次性接受不了太大的sql语句）
  #  defaultBatchSize: 500

  # Mapper文件刷新线程
  mapper:
    refresh:
      enabled: true
#      delaySeconds: 60
#      sleepSeconds: 3
#      mappingPath: mappings

# Web 相关
web:
  #  # AJAX 接受参数名和请求头名（v4.3.0）
  #  ajaxParamName: __ajax
  #  ajaxHeaderName: x-ajax
  #
  #  # 是否默认对结果进行统一包装为：{ code: 200, msg: "", data: {} | [] }（v5.8.1）
  #  # 注意：如果设置为 true 会对前端页面访问产生影响，暂时只为系统纯接口提供开启使用。
  #  isDefaultResult: false
  #
  #  # 开启对接口结果数据进行包装的请求参数名和请求头名（v5.8.1）
  resultParamName: __data
  resultHeaderName: x-header
  #
  #  # MVC 视图相关
  #  view:
  #
  #    # 系统主题名称，主题视图优先级最高，如果主题下无这个视图文件则访问默认视图
  #    # 引入页面头部：'/themes/'+themeName+'/include/header.html'
  #    # 引入页面尾部：'/themes/'+themeName+'/include/footer.html'
  #    themeName: default
  #
  #    # 使用智能参数接收器，同时支持 JSON 和 FormData 的参数接受
  #    smartMethodArgumentResolver: true
  #
  #    # 使用 .json、.xml 后缀匹配返回视图数据（Spring官方已不推荐使用）
  #    favorPathExtension: false
  #    # 使用 __ajax=json、__ajax=xml 后缀匹配返回视图数据
  #    favorParameter: true
  #    # 使用 x-ajax=json、x-ajax=xml 请求头匹配返回视图数据
  #    favorHeader: true

  # MVC 拦截器
  interceptor:
    # 后台管理日志记录拦截器
    log:
      enabled: true
      addPathPatterns: >
        ${adminPath}/**
      excludePathPatterns: >
        ${adminPath}/index,
        ${adminPath}/login,
        ${adminPath}/desktop,
        ${adminPath}/authInfo,
        ${adminPath}/menuRoute,
        ${adminPath}/switchSkin/*,
        ${adminPath}/index/menuTree,
        ${adminPath}/sys/online/count,
        ${adminPath}/**/server/rtInfo,
        ${adminPath}/**/treeData,
        ${adminPath}/file/**,
        ${adminPath}/tags/*,
        ${adminPath}/msg/**

    # 前台自动切换到手机视图拦截器
    mobile:
      enabled: false
      addPathPatterns: >
        ${frontPath}/**
      excludePathPatterns: ~

  #  # 静态文件后缀，过滤静态文件，以提高访问性能。
  #  staticFile: .css,.js,.map,.png,.jpg,.gif,.jpeg,.webp,.bmp,.ico,.swf,.psd,.htc,.crx,.xpi,.exe,.ipa,.apk,.otf,.eot,.svg,.ttf,.woff,.woff2
  #
  #  # 静态文件后缀，排除的url路径，指定哪些uri路径不进行静态文件过滤。
  staticFileExcludeUri: /druid/,/static/modules/msg/
  #
  #  # 静态资源路径前缀，可做 CDN 加速优化，默认前面增加 ctxPath 前缀，如果前面写 “//” 两个斜杠 或 包含 “://” 不加 ctxPath。
  #  staticPrefix: /static
  #
  #  # 严格模式（更严格的数据安全验证）
  strictMode: true
  #
  #  # 所有请求信息将进行xss过滤，这里列出不被xss过滤的地址
  #  xssFilterExcludeUri: /ureport/,/visual/
  #
  #  # 自定义正则表达式验证（主键、登录名）
  #  validator:
  #    id: '[a-zA-Z0-9_\-/#\u4e00-\u9fa5]{0,64}'
  #    user.loginCode: '[a-zA-Z0-9_\u4e00-\u9fa5]{4,20}'
  #
  #  # 默认的日期格式（JsonMapper）
  #  json:
  #    defaultDateFormat: yyyy-MM-dd HH:mm:ss
  #
  #  # 默认不启用（为兼用旧版保留，建议使用 CORS）
  #  jsonp:
  #    enabled: false
  #    callback: __callback

  # 核心模块的Web功能开启（其它微服务时设为false）
  core:
    enabled: true

  # 在线API文档工具
  swagger:
    enabled: false
# 错误页面500.html是否输出错误信息（正式环境，为提供安全性可设置为false）
error:
  page:
    printErrorInfo: false

#======================================#
#======== FileUpload settings =========#
#======================================#

# 文件上传
file:
  enabled: true

  #  # 文件上传根路径，设置路径中不允许包含“userfiles”，在指定目录中系统会自动创建userfiles目录，如果不设置默认为contextPath路径
  #  #baseDir: D:/jeesite
  #
  #  # 上传文件的相对路径（支持：yyyy、MM、dd、HH、mm、ss、E、bizType、corpCode、userCode、userType、userCache中的key）
  #  uploadPath: '{yyyy}{MM}/'
  #
  #  # 上传单个文件最大字节（500M），在这之上还有 > Tomcat限制 > Nginx限制，等，此设置会覆盖 spring.http.multipart.maxFileSize 设置
  #  maxFileSize: '500*1024*1024'
  #
  # 设置允许上传的文件后缀（全局设置）
  imageAllowSuffixes: .gif,.bmp,.jpeg,.jpg,.ico,.png,.tif,.tiff,webp,
  mediaAllowSuffixes: .flv,.swf,.mkv,webm,.mid,.mov,.mp3,.mp4,.m4v,.mpc,.mpeg,.mpg,.swf,.wav,.wma,.wmv,.avi,.rm,.rmi,.rmvb,.aiff,.asf,.ogg,.ogv,
  fileAllowSuffixes: .doc,.docx,.rtf,.xls,.xlsx,.csv,.ppt,.pptx,.pdf,.vsd,.txt,.md,.xml,.rar,.zip,.7z,.tar,.tgz,.jar,.gz,.gzip,.bz2,.cab,.iso,.ipa,.apk,.dwg,
#
#  # 允许上传的文件内容类型（图片、word、excel、ppt）防止修改后缀恶意上传文件（默认不启用验证）
#  #allowContentTypes: image/jpeg,image/gif,image/bmp,image/png,image/x-png,
#  #  application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,
#  #  application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,
#  #  application/vnd.ms-powerpoint,application/vnd.openxmlformats-officedocument.presentationml.presentation
#
#  # 上传图片自动压缩宽高，指定为 -1 不进行压缩（全局设置）  v4.1.7
#  imageMaxWidth: 1024
#  imageMaxHeight: 768
#
#  # 缩略图设定，生成的尺寸（宽x高,宽x高）、格式（jpg,png）  v5.5.0
#  #imageThumbConfig: 150x150,300x300
#  #imageThumbFormat: jpg
#
#  # 是否启用秒传
#  checkmd5: true
#
#  # 是否开启分片上传
#  chunked: true
#  # 分片大小，单位字节（10M）
#  chunkSize: '10*1024*1024'
#  # 最大上传线程数
#  threads: 3
#
#  # 是否启用检查点（支持断点续传，上传）
#  checkpoint: true
#
#  # 是否用文件流方式下载（支持断点续传，下载）
#  isFileStreamDown: true
#
#  # 默认的预览类型（true、oss）
#  preview: true

# 视频转码
#video:
#
#  # 视频格式转换  ffmpeg.exe 所放的路径
#  ffmpegFile: d:/tools/video/ffmpeg-4.9/bin/ffmpeg.exe
#  #ffmpegFile: d:/tools/video/libav-10.6-win64/bin/avconv.exe
#
#  # 视频格式转换  mencoder.exe 所放的路径
#  mencoderFile: d:/tools/video/mencoder-4.9/mencoder.exe
#
#  # 将mp4视频的元数据信息转到视频第一帧
#  qtFaststartFile: d:/tools/video/qt-faststart/qt-faststart.exe

# 文件管理是否启用租户模式
#filemanager:
#  useCorpModel: false

#======================================#
#========== Message settings ==========#
#======================================#

# 消息提醒中心（专业版）
msg:
  enabled: true

#  # 是否开启实时发送消息（保存消息后立即检查未读消息并发送），分布式部署下请单独配置消息发送服务，不建议开启此选项。
#  realtime:
#    # 是否开启
#    enabled: true
#    # 消息实时推送任务Bean名称
#    beanName: msgLocalPushTask
#    # 消息推送线程池
#    pushTaskPool:
#      corePoolSize: 5
#      maxPoolSize: 20
#      keepAliveSeconds: 60
#
#  # 推送失败次数，如果推送次数超过了设定次数，仍不成功，则放弃并保存到历史
#  pushFailNumber: 3
#
#  # 邮件发送参数
#  email:
#    beanName: emailSendService
#    fromAddress: <EMAIL>
#    fromPassword: 123456
#    fromHostName: smtp.163.com
#    sslOnConnect: false
#    sslSmtpPort: 994
#
#  # 短信网关
#  sms:
#    beanName: smsSendService
#    url: http://localhost:80/msg/sms/send
#    data: username=jeesite&password=jeesite.com
#    prefix: 【JeeSite】
#    suffix: ~

# 文档转换工具
#jodconverter:
##
##  # 方式一：本地转换服务
#  local:
#    enabled: true
##
##    # 请安装 LibreOffice 软件，用于转码 office 文件为浏览器可预览文件
##    # 下载地址：https://zh-cn.libreoffice.org/download/libreoffice/
##
##    # ========== 一定要指定 LibreOffice 的主目录 ==========
##
##    # Windows 环境：
##    # officeHome: "C:/Program Files/LibreOffice"
##    # LibreOffice 便携版，需要加 /App/libreoffice 后缀
#    officeHome: C:/Program Files/LibreOffice
##
##    # CentOS Linux 环境：
##    #   方法1：yum -y install libreoffice
##    #   方法2：https://zh-cn.libreoffice.org/get-help/install-howto/linux/
##    # Linux 中文字体乱码解决：
##    #   方法1：`yum groupinstall "fonts"`
##    #   方法2：
##    #     1、上传 C:\Windows\Fonts 下的字体到 /usr/share/fonts/windows 目录
##    #     2、执行命令： chmod 644 /usr/share/fonts/windows/* && fc-cache -fv
##    # 安装完成后可执行这个命令验证是否成功：liberoffice --version
##    # officeHome: /usr/lib64/libreoffice
##
##    # MacOS 苹果操作系统环境：
##    #   安装：brew install libreoffice
##    # officeHome: /Applications/LibreOffice.app/Contents
##
##    # 监听端口
#    portNumbers: 2000
##
##  # 方式二：远程转换服务（v5.1.0+），暂不支持 xlsx 中的图片转换
##  #        联系售后获取 jeesite-web-jodconverter 转换服务
#  remote:
#    enabled: false
#    url: http://127.0.0.1:8002
mzt:
  accountId: MZT_API
  sm2:
    pbk: 042560c74bcc717e94df8c61e3b02da66b7415dfd23fa048e700dbf58a086e7f685321f0cd6875836aa34c7414974d8603d91948be730f3724a7c8108ab01d5629
  expiresIn: 3600
szps:
    # 数字屏山分配给住房保障的账号
    accountId-szps: A001
    sm2:
      # 加签秘钥-本地私钥
      prk-my: 5CD86BA348BDF113D2512FE4C9E69FD12D5290290AE412D25BF6F7DA0A757652
      # 验签秘钥-本地公钥
      pbk-my: 04F662FB68FA6F42DAC57F3B0C2DB8B838F43109450FAA54A0353C0718046B2554CACE663563759A22575526892C043D97D4FFEBB6ECA017060EA1B695ACBE3A01
    expiresIn: 3600
    # 数字空间接口地址
    server: http://117.25.28.209:4348
szkj:
  # 住房保障分配给数字空间的账号
  accountId-my: SZKJ-API
  # 数字空间分配给住房保障的账号
  accountId-szkj: C88A6ADB99DE4452BA7A5B0FDE9D2430
  sm2:
    # 加签秘钥-本地私钥
    prk-my: 3AB792197F8D7942D76BBE55DD3286FD922403D9EB5746021F7FD649658574A2
    # 验签秘钥-本地公钥
    pbk-my: 0422AAD0D67A9536B1E3D744AD54D1CCE2A672FA3FF10343AA921AC262D8714B665DA2C55CF0C69A54AA37BA809C2CB4564859F94746FE9E4DC9BCF44E9BDBCA18
    # 验签秘钥-数字空间公钥
    pbk-szkj: 042560c74bcc717e94df8c61e3b02da66b7415dfd23fa048e700dbf58a086e7f685321f0cd6875836aa34c7414974d8603d91948be730f3724a7c8108ab01d5629
  expiresIn: 3600
  # 数字空间接口地址
  server: https://220.250.1.58:12444
hjpt: #数据汇聚共享平台
  app-id: 737917aaafc76839440357f30c65b7f9
  app-key: ec0a51be3c19aa6af3729f6f3da4b1fe
  server: https://59.204.232.165
  sm2:
    #汇聚平台公钥-base64编码
    pkb-hjpt: MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEKJmFIq/Y48kpRmpzHwdkDEr/K7aOcSt/GDfV7+8d+Uq1jKvY+jhG7souKq/PZpfjZJPrJAEC+wOg71ouuGeKFw==

zcpt:
  server: https://220.250.1.58:12444
  systemId: C88A6ADB99DE4452BA7A5B0FDE9D2430
  # 组织机构全部权限 组织编码多个用逗号隔开
  allOrgCode:
  # 组织机构全部权限 角色编码多个用逗号隔开
  allRoleCode:
  # 同步级别： 1-只同步组织架构+用户  2-同步组织架构+用户+角色+权限
  syncLevel: 1
  # 应用支撑平台
  sm4:
    # 加签秘钥-本地私钥
    prk-my: 3AB792197F8D7942D76BBE55DD3286FD922403D9EB5746021F7FD649658574A2
  sm3:
    # 验签秘钥-本地公钥
    pbk-my: 3AB792197F8D7942D76BBE55DD3286FD922403D9EB5746021F7FD649658574A2

# 人员类型配置
personnel:
  types:
    - code: "1"
      name: "在编在职"
    - code: "2"
      name: "离退休"
    - code: "3"
      name: "编外合同制干部职工"

# 职级配置
position:
  levels:
    - code: "1"
      name: "正厅级"
    - code: "2"
      name: "副厅级"
    - code: "3"
      name: "正高职称"
    - code: "4"
      name: "正处级"
    - code: "5"
      name: "副处级"
    - code: "6"
      name: "副高职称"
    - code: "7"
      name: "科级"
    - code: "8"
      name: "中级职称"
    - code: "9"
      name: "一般干部职工"



tianditu:
  url: https://s0.fjmap.net
  baseUrl: http://*************:82/api?v=4.0
  baseInternetUrl: http://api.tianditu.gov.cn/api?v=4.0&tk=18fa918fd89912421adc846feab670ad

server_address:
  zwww: http://zfbz.fj.cegn.cn:8010/hsobs/admin
  internet: http://**************:8981/hsobs/admin


#titleAssigned: 住房保障管理系统
#productNameAssigned: 住房保障管理系统

szgz:
  # 资产一体化平台接口地址
  server: http://************:57994
  # 资产数据信息查询接口
  assetPath: /asset-web/asset-data-exchange-server/openApi/v1/basAssetInfo/getPageStockAssetDetail

allowHosts: 127.0.0.1:8980,localhost:8980

hmt: 0

# 数据脱敏配置文件
dataMask:
  # 全局脱敏开关
  enabled: true

  # 脱敏规则配置
  rules:
    # 姓名脱敏规则
    name:
      enabled: true
      description: "姓名脱敏：保留姓氏，名字用*替换"

    # 手机号脱敏规则
    mobile:
      enabled: true
      description: "手机号脱敏：保留前3位和后4位"

    # 身份证脱敏规则
    idCard:
      enabled: true
      description: "身份证脱敏：保留前6位和后4位"

    # 银行卡脱敏规则
    bankCard:
      enabled: true
      description: "银行卡脱敏：保留前4位和后4位"

    # 邮箱脱敏规则
    email:
      enabled: true
      description: "邮箱脱敏：保留前3位和@后域名"

    # 地址脱敏规则
    address:
      enabled: true
      description: "地址脱敏：保留前6位，后面用*替换"

  # 权限配置
  permissions:
    # 查看敏感数据权限
    viewSensitive: "sys:data:viewSensitive"

    # 查看身份证权限
    viewIdCard: "house:owner:viewIdCard"

    # 查看手机号权限
    viewMobile: "house:owner:viewMobile"

    # 查看银行卡权限
    viewBankCard: "house:owner:viewBankCard"

  # 角色配置
  roles:
    # 管理员角色（不脱敏）
    admin:
      enabled: false
      description: "管理员角色，不进行数据脱敏"

    # 普通用户角色（脱敏）
    user:
      enabled: true
      description: "普通用户角色，进行数据脱敏"

    # 查看者角色（脱敏）
    viewer:
      enabled: true
      description: "查看者角色，进行数据脱敏"

  # 页面级别配置
  pages:
    # 用户管理页面
    "/user/":
      enabled: true
      excludeFields: ["id", "createTime", "updateTime"]
      description: "用户管理页面脱敏配置"

    # 房屋管理页面
    "/house/":
      enabled: true
      excludeFields: ["houseId", "estateId"]
      description: "房屋管理页面脱敏配置"

    # 申请管理页面
    "/apply/":
      enabled: true
      excludeFields: []
      description: "申请管理页面脱敏配置"

  # 接口级别配置
  apis:
    # 排除的API路径（不进行脱敏）
    excludePaths:
      - "/api/login"
      - "/api/logout"
      - "/api/captcha"
      - "/static/"
      - "/assets/"
      - "/js/"
      - "/css/"
      - "/images/"

    # 需要脱敏的API路径
    includePaths:
      - "/api/house/"
      - "/api/user/"
      - "/api/apply/"

  # 日志配置
  logging:
    # 是否记录脱敏操作日志
    enabled: true

    # 日志级别
    level: "INFO"

    # 是否记录脱敏前后的数据对比
    logDataComparison: false

  # 性能配置
  performance:
    # 是否启用缓存
    cacheEnabled: true

    # 缓存过期时间（秒）
    cacheExpireTime: 3600

    # 批处理大小
    batchSize: 1000

#======================================#
#========== Project settings ==========#
#======================================#
