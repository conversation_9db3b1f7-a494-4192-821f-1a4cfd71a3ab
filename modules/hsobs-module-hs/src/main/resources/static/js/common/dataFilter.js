/**
 * 数据脱敏过滤器
 * 用于对返回的JSON数据进行统一的脱敏处理
 */
(function(window) {
    'use strict';

    var DataFilter = {
        
        // 脱敏配置
        config: {
            // 身份证号脱敏规则
            idCard: {
                pattern: /^(\d{6})\d{8}(\d{4})$/,
                replacement: '$1********$2'
            },
            // 手机号脱敏规则
            mobile: {
                pattern: /^(\d{3})\d{4}(\d{4})$/,
                replacement: '$1****$2'
            },
            // 银行卡号脱敏规则
            bankCard: {
                pattern: /^(\d{4})\d+(\d{4})$/,
                replacement: '$1****$2'
            },
            // 姓名脱敏规则
            name: {
                pattern: /^(.)(.+)(.)$/,
                replacement: function(match, first, middle, last) {
                    return first + '*'.repeat(middle.length) + (last || '');
                }
            },
            // 邮箱脱敏规则
            email: {
                pattern: /^(.{1,3}).*@(.*)$/,
                replacement: '$1***@$2'
            }
        },

        // 需要脱敏的字段名称（支持正则匹配）
        sensitiveFields: [
            /idCard/i,           // 身份证
            /mobile/i,           // 手机号
            /phone/i,            // 电话
            /bankCard/i,         // 银行卡
            /cardNo/i,           // 卡号
            /userName/i,         // 用户名
            /realName/i,         // 真实姓名
            /name$/i,            // 以name结尾的字段
            /email/i,            // 邮箱
            /address/i           // 地址
        ],

        /**
         * 判断字段是否需要脱敏
         * @param {string} fieldName 字段名
         * @returns {boolean}
         */
        isSensitiveField: function(fieldName) {
            if (!fieldName || typeof fieldName !== 'string') {
                return false;
            }
            
            return this.sensitiveFields.some(function(pattern) {
                return pattern.test(fieldName);
            });
        },

        /**
         * 根据字段名判断脱敏类型
         * @param {string} fieldName 字段名
         * @returns {string} 脱敏类型
         */
        getSensitiveType: function(fieldName) {
            if (/idCard/i.test(fieldName)) return 'idCard';
            if (/mobile|phone/i.test(fieldName)) return 'mobile';
            if (/bankCard|cardNo/i.test(fieldName)) return 'bankCard';
            if (/userName|realName|name$/i.test(fieldName)) return 'name';
            if (/email/i.test(fieldName)) return 'email';
            return 'default';
        },

        /**
         * 对单个值进行脱敏处理
         * @param {string} value 原始值
         * @param {string} type 脱敏类型
         * @returns {string} 脱敏后的值
         */
        maskValue: function(value, type) {
            if (!value || typeof value !== 'string') {
                return value;
            }

            var rule = this.config[type];
            if (!rule) {
                // 默认脱敏规则：保留前后各1位，中间用*替换
                if (value.length <= 2) return value;
                var stars = '*'.repeat(Math.max(value.length - 2, 1));
                return value.charAt(0) + stars + value.charAt(value.length - 1);
            }

            if (typeof rule.replacement === 'function') {
                return value.replace(rule.pattern, rule.replacement);
            } else {
                return value.replace(rule.pattern, rule.replacement);
            }
        },

        /**
         * 递归处理对象进行脱敏
         * @param {*} data 数据对象
         * @param {boolean} enabled 是否启用脱敏
         * @returns {*} 处理后的数据
         */
        filterData: function(data, enabled) {
            // 如果未启用脱敏，直接返回原数据
            if (!enabled) {
                return data;
            }

            if (data === null || data === undefined) {
                return data;
            }

            // 处理数组
            if (Array.isArray(data)) {
                return data.map(function(item) {
                    return DataFilter.filterData(item, enabled);
                });
            }

            // 处理对象
            if (typeof data === 'object') {
                var result = {};
                for (var key in data) {
                    if (data.hasOwnProperty(key)) {
                        var value = data[key];
                        
                        // 如果是敏感字段且值为字符串，进行脱敏处理
                        if (this.isSensitiveField(key) && typeof value === 'string') {
                            var type = this.getSensitiveType(key);
                            result[key] = this.maskValue(value, type);
                        } else {
                            // 递归处理嵌套对象
                            result[key] = this.filterData(value, enabled);
                        }
                    }
                }
                return result;
            }

            // 基本类型直接返回
            return data;
        },

        /**
         * 扩展jQuery的DataTable插件，添加脱敏功能
         */
        extendDataTable: function() {
            if (typeof $ !== 'undefined' && $.fn.dataTable) {
                // 扩展DataTable的默认配置
                $.extend(true, $.fn.dataTable.defaults, {
                    dataMask: {
                        enabled: false, // 默认不启用
                        excludeColumns: [] // 排除的列
                    }
                });

                // 重写DataTable的数据处理
                var originalAjax = $.fn.dataTable.Api.prototype.ajax;
                if (originalAjax && originalAjax.json) {
                    var originalJson = originalAjax.json;
                    originalAjax.json = function() {
                        var data = originalJson.call(this);
                        var settings = this.settings()[0];
                        
                        if (settings.dataMask && settings.dataMask.enabled) {
                            data = DataFilter.filterData(data, true);
                        }
                        
                        return data;
                    };
                }
            }
        },

        /**
         * 初始化脱敏过滤器
         * @param {Object} options 配置选项
         */
        init: function(options) {
            options = options || {};
            
            // 合并配置
            if (options.config) {
                $.extend(true, this.config, options.config);
            }
            
            if (options.sensitiveFields) {
                this.sensitiveFields = this.sensitiveFields.concat(options.sensitiveFields);
            }

            // 扩展DataTable
            this.extendDataTable();

            // 全局Ajax拦截器（可选）
            if (options.enableGlobalFilter && typeof $ !== 'undefined') {
                this.setupGlobalAjaxFilter();
            }
        },

        /**
         * 设置全局Ajax过滤器
         */
        setupGlobalAjaxFilter: function() {
            var self = this;
            
            // jQuery Ajax拦截
            $(document).ajaxSuccess(function(event, xhr, settings) {
                if (settings.dataMask && settings.dataMask.enabled) {
                    try {
                        var data = JSON.parse(xhr.responseText);
                        var filteredData = self.filterData(data, true);
                        // 这里可以根据需要处理过滤后的数据
                    } catch (e) {
                        // 忽略非JSON响应
                    }
                }
            });
        }
    };

    // 暴露到全局
    window.DataFilter = DataFilter;

    // 如果存在jQuery，自动初始化
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            DataFilter.init();
        });
    }

})(window);
