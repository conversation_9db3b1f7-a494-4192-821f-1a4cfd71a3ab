/**
 * 数据脱敏过滤器配置文件
 * 可以根据项目需求自定义脱敏规则
 */
(function(window) {
    'use strict';

    // 脱敏配置
    var DataMaskConfig = {
        
        // 是否全局启用脱敏（可通过后台配置控制）
        globalEnabled: false,
        
        // 脱敏规则配置
        rules: {
            // 身份证号脱敏：保留前6位和后4位
            idCard: {
                pattern: /^(\d{6})\d{8}(\d{4})$/,
                replacement: '$1********$2',
                description: '身份证号脱敏'
            },
            
            // 手机号脱敏：保留前3位和后4位
            mobile: {
                pattern: /^(\d{3})\d{4}(\d{4})$/,
                replacement: '$1****$2',
                description: '手机号脱敏'
            },
            
            // 银行卡号脱敏：保留前4位和后4位
            bankCard: {
                pattern: /^(\d{4})\d+(\d{4})$/,
                replacement: '$1****$2',
                description: '银行卡号脱敏'
            },
            
            // 姓名脱敏：保留姓氏，名字用*替换
            name: {
                pattern: /^(.)(.+)$/,
                replacement: function(match, first, rest) {
                    return first + '*'.repeat(rest.length);
                },
                description: '姓名脱敏'
            },
            
            // 邮箱脱敏：保留前3位和@后的域名
            email: {
                pattern: /^(.{1,3}).*@(.*)$/,
                replacement: '$1***@$2',
                description: '邮箱脱敏'
            },
            
            // 地址脱敏：保留前面部分，后面用*替换
            address: {
                pattern: /^(.{6})(.{4,})$/,
                replacement: function(match, prefix, suffix) {
                    return prefix + '*'.repeat(Math.min(suffix.length, 6));
                },
                description: '地址脱敏'
            }
        },

        // 敏感字段匹配规则
        sensitiveFields: [
            // 身份证相关
            { pattern: /idCard/i, type: 'idCard' },
            { pattern: /identityCard/i, type: 'idCard' },
            { pattern: /sfzh/i, type: 'idCard' },
            
            // 手机号相关
            { pattern: /mobile/i, type: 'mobile' },
            { pattern: /phone/i, type: 'mobile' },
            { pattern: /sjh/i, type: 'mobile' },
            
            // 银行卡相关
            { pattern: /bankCard/i, type: 'bankCard' },
            { pattern: /cardNo/i, type: 'bankCard' },
            { pattern: /yhkh/i, type: 'bankCard' },
            
            // 姓名相关
            { pattern: /userName/i, type: 'name' },
            { pattern: /realName/i, type: 'name' },
            { pattern: /name$/i, type: 'name' },
            { pattern: /xm$/i, type: 'name' },
            
            // 邮箱相关
            { pattern: /email/i, type: 'email' },
            { pattern: /mail/i, type: 'email' },
            
            // 地址相关
            { pattern: /address/i, type: 'address' },
            { pattern: /addr/i, type: 'address' },
            { pattern: /dz$/i, type: 'address' }
        ],

        // 页面级别的脱敏配置
        pageConfigs: {
            // 用户管理页面
            '/user/': {
                enabled: true,
                excludeFields: ['id', 'createTime', 'updateTime']
            },
            
            // 房屋管理页面
            '/house/': {
                enabled: true,
                excludeFields: ['houseId', 'estateId']
            },
            
            // 申请管理页面
            '/apply/': {
                enabled: true,
                excludeFields: []
            }
        },

        // 角色权限配置
        roleConfigs: {
            // 管理员不脱敏
            'admin': {
                enabled: false
            },
            
            // 普通用户脱敏
            'user': {
                enabled: true
            },
            
            // 查看者脱敏
            'viewer': {
                enabled: true
            }
        }
    };

    // 获取当前页面的脱敏配置
    function getPageConfig() {
        var currentPath = window.location.pathname;
        
        for (var path in DataMaskConfig.pageConfigs) {
            if (currentPath.indexOf(path) !== -1) {
                return DataMaskConfig.pageConfigs[path];
            }
        }
        
        return { enabled: DataMaskConfig.globalEnabled };
    }

    // 获取当前用户角色的脱敏配置
    function getRoleConfig() {
        // 这里需要根据实际的用户角色获取方式来实现
        var userRole = window.currentUserRole || 'user'; // 假设从全局变量获取
        
        return DataMaskConfig.roleConfigs[userRole] || { enabled: true };
    }

    // 判断是否应该启用脱敏
    function shouldEnableMask() {
        var pageConfig = getPageConfig();
        var roleConfig = getRoleConfig();
        
        // 角色配置优先级最高
        if (roleConfig.enabled === false) {
            return false;
        }
        
        // 然后是页面配置
        return pageConfig.enabled || false;
    }

    // 初始化脱敏配置
    function initDataMask() {
        if (typeof DataFilter !== 'undefined') {
            // 应用配置到DataFilter
            DataFilter.init({
                config: DataMaskConfig.rules,
                sensitiveFields: DataMaskConfig.sensitiveFields.map(function(item) {
                    return item.pattern;
                }),
                enableGlobalFilter: shouldEnableMask()
            });
            
            console.log('数据脱敏过滤器已初始化，启用状态：', shouldEnableMask());
        }
    }

    // 暴露配置到全局
    window.DataMaskConfig = DataMaskConfig;
    window.shouldEnableMask = shouldEnableMask;
    window.initDataMask = initDataMask;

    // 自动初始化
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            initDataMask();
        });
    } else {
        // 如果没有jQuery，使用原生方式
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initDataMask);
        } else {
            initDataMask();
        }
    }

})(window);
