/**
 * 表格脱敏助手
 * 简化在表格中使用脱敏功能的工具
 */
(function(window) {
    'use strict';

    var TableMaskHelper = {
        
        /**
         * 创建脱敏formatter
         * @param {string} fieldType 字段类型 (name, mobile, idCard, email, address)
         * @returns {function} formatter函数
         */
        createMaskFormatter: function(fieldType) {
            return function(val, obj, row, act) {
                // 如果值为空或不是字符串，直接返回
                if (!val || typeof val !== 'string') {
                    return val || '';
                }
                
                // 检查是否需要脱敏
                if (typeof shouldEnableMask === 'function' && shouldEnableMask()) {
                    if (typeof DataFilter !== 'undefined') {
                        return DataFilter.maskValue(val, fieldType);
                    }
                }
                
                return val;
            };
        },

        /**
         * 创建通用脱敏formatter（自动检测字段类型）
         * @param {string} fieldName 字段名
         * @returns {function} formatter函数
         */
        createAutoMaskFormatter: function(fieldName) {
            return function(val, obj, row, act) {
                if (!val || typeof val !== 'string') {
                    return val || '';
                }
                
                if (typeof shouldEnableMask === 'function' && shouldEnableMask()) {
                    if (typeof DataFilter !== 'undefined' && DataFilter.isSensitiveField(fieldName)) {
                        var type = DataFilter.getSensitiveType(fieldName);
                        return DataFilter.maskValue(val, type);
                    }
                }
                
                return val;
            };
        },

        /**
         * 快速脱敏方法
         */
        mask: {
            // 姓名脱敏
            name: function(val) {
                if (!val || typeof val !== 'string') return val;
                if (val.length <= 1) return val;
                return val.charAt(0) + '*'.repeat(val.length - 1);
            },
            
            // 手机号脱敏
            mobile: function(val) {
                if (!val || typeof val !== 'string') return val;
                return val.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');
            },
            
            // 身份证脱敏
            idCard: function(val) {
                if (!val || typeof val !== 'string') return val;
                return val.replace(/^(\d{6})\d{8}(\d{4})$/, '$1********$2');
            },
            
            // 邮箱脱敏
            email: function(val) {
                if (!val || typeof val !== 'string') return val;
                return val.replace(/^(.{1,3}).*@(.*)$/, '$1***@$2');
            },
            
            // 地址脱敏
            address: function(val) {
                if (!val || typeof val !== 'string') return val;
                if (val.length <= 6) return val;
                return val.substring(0, 6) + '*'.repeat(Math.min(val.length - 6, 6));
            }
        },

        /**
         * 为表格列配置添加脱敏功能
         * @param {Array} columns 表格列配置
         * @returns {Array} 处理后的列配置
         */
        addMaskToColumns: function(columns) {
            if (!Array.isArray(columns)) {
                return columns;
            }
            
            return columns.map(function(column) {
                if (column.name && typeof DataFilter !== 'undefined' && DataFilter.isSensitiveField(column.name)) {
                    // 保存原有的formatter
                    var originalFormatter = column.formatter;
                    var fieldType = DataFilter.getSensitiveType(column.name);
                    
                    // 创建新的formatter
                    column.formatter = function(val, obj, row, act) {
                        // 先执行原有的formatter
                        var result = originalFormatter ? originalFormatter.call(this, val, obj, row, act) : val;
                        
                        // 如果需要脱敏且结果是字符串，进行脱敏处理
                        if (typeof shouldEnableMask === 'function' && shouldEnableMask() && 
                            typeof result === 'string' && result.trim() !== '') {
                            return DataFilter.maskValue(result, fieldType);
                        }
                        
                        return result;
                    };
                }
                
                return column;
            });
        },

        /**
         * 检查当前用户是否有查看敏感数据的权限
         * @returns {boolean}
         */
        hasViewSensitivePermission: function() {
            // 这里应该根据实际的权限系统来实现
            // 示例实现
            if (typeof window.currentUser !== 'undefined') {
                var user = window.currentUser;
                return user.permissions && user.permissions.includes('VIEW_SENSITIVE_DATA');
            }
            return false;
        },

        /**
         * 初始化表格脱敏功能
         * @param {Object} options 配置选项
         */
        init: function(options) {
            options = options || {};
            
            // 设置全局脱敏状态
            if (typeof DataMaskConfig !== 'undefined') {
                // 根据用户权限设置脱敏状态
                DataMaskConfig.globalEnabled = !this.hasViewSensitivePermission();
            }
            
            console.log('表格脱敏助手已初始化');
        }
    };

    // 暴露到全局
    window.TableMaskHelper = TableMaskHelper;

    // 自动初始化
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            TableMaskHelper.init();
        });
    }

})(window);

// 使用示例：
/*
// 1. 在表格列配置中使用
{
    header: '姓名',
    name: 'userName',
    formatter: TableMaskHelper.createMaskFormatter('name')
}

// 2. 自动检测字段类型
{
    header: '手机号',
    name: 'mobile',
    formatter: TableMaskHelper.createAutoMaskFormatter('mobile')
}

// 3. 快速脱敏
var maskedName = TableMaskHelper.mask.name('张三');
var maskedMobile = TableMaskHelper.mask.mobile('13812345678');

// 4. 为整个表格配置添加脱敏
var columns = [
    {header: '姓名', name: 'userName'},
    {header: '手机号', name: 'mobile'},
    {header: '身份证', name: 'idCard'}
];
columns = TableMaskHelper.addMaskToColumns(columns);
*/
