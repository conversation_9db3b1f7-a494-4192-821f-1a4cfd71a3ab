/**
 * 数据脱敏过滤器使用示例
 * 展示如何在不同场景下使用脱敏功能
 */

// 使用示例1：在表格formatter中使用
function createTableWithMask() {
    return {
        // 普通字段的formatter
        nameFormatter: function(val, obj, row, act) {
            // 检查是否需要脱敏
            if (shouldEnableMask()) {
                return DataFilter.maskValue(val, 'name');
            }
            return val;
        },
        
        // 手机号formatter
        mobileFormatter: function(val, obj, row, act) {
            if (shouldEnableMask()) {
                return DataFilter.maskValue(val, 'mobile');
            }
            return val;
        },
        
        // 身份证formatter
        idCardFormatter: function(val, obj, row, act) {
            if (shouldEnableMask()) {
                return DataFilter.maskValue(val, 'idCard');
            }
            return val;
        },
        
        // 通用脱敏formatter
        sensitiveFormatter: function(val, obj, row, act, fieldName) {
            if (shouldEnableMask() && DataFilter.isSensitiveField(fieldName)) {
                var type = DataFilter.getSensitiveType(fieldName);
                return DataFilter.maskValue(val, type);
            }
            return val;
        }
    };
}

// 使用示例2：对整个数据对象进行脱敏
function maskDataObject(data) {
    return DataFilter.filterData(data, shouldEnableMask());
}

// 使用示例3：Ajax请求后的数据处理
function handleAjaxResponse(response) {
    // 对返回的数据进行脱敏处理
    var maskedData = DataFilter.filterData(response, shouldEnableMask());
    
    // 使用处理后的数据
    return maskedData;
}

// 使用示例4：扩展现有的表格配置
function extendTableConfig(originalConfig) {
    // 为所有列添加脱敏功能
    if (originalConfig.columns) {
        originalConfig.columns.forEach(function(column) {
            if (column.name && DataFilter.isSensitiveField(column.name)) {
                var originalFormatter = column.formatter;
                var sensitiveType = DataFilter.getSensitiveType(column.name);
                
                column.formatter = function(val, obj, row, act) {
                    // 先执行原有的formatter
                    var result = originalFormatter ? originalFormatter(val, obj, row, act) : val;
                    
                    // 如果需要脱敏且结果是字符串，进行脱敏处理
                    if (shouldEnableMask() && typeof result === 'string') {
                        return DataFilter.maskValue(result, sensitiveType);
                    }
                    
                    return result;
                };
            }
        });
    }
    
    return originalConfig;
}

// 使用示例5：创建脱敏开关控制
function createMaskToggle() {
    var $toggle = $('<div class="mask-toggle">' +
        '<label>' +
        '<input type="checkbox" id="dataMaskToggle"> 启用数据脱敏' +
        '</label>' +
        '</div>');
    
    $toggle.find('#dataMaskToggle').change(function() {
        var enabled = $(this).is(':checked');
        
        // 更新全局配置
        DataMaskConfig.globalEnabled = enabled;
        
        // 重新加载表格数据
        if (typeof table !== 'undefined' && table.ajax) {
            table.ajax.reload();
        }
        
        console.log('数据脱敏已' + (enabled ? '启用' : '禁用'));
    });
    
    return $toggle;
}

// 使用示例6：批量处理表格数据
function batchMaskTableData(tableData) {
    if (!Array.isArray(tableData)) {
        return tableData;
    }
    
    return tableData.map(function(row) {
        var maskedRow = {};
        
        for (var key in row) {
            if (row.hasOwnProperty(key)) {
                var value = row[key];
                
                if (shouldEnableMask() && DataFilter.isSensitiveField(key) && typeof value === 'string') {
                    var type = DataFilter.getSensitiveType(key);
                    maskedRow[key] = DataFilter.maskValue(value, type);
                } else {
                    maskedRow[key] = value;
                }
            }
        }
        
        return maskedRow;
    });
}

// 使用示例7：在现有的rentalHouseList.html中集成
function integrateWithRentalHouseList() {
    // 可以在表格配置中添加脱敏功能
    var tableFormatters = createTableWithMask();
    
    // 示例：修改现有的formatter
    return {
        // 用户姓名列
        userNameFormatter: tableFormatters.nameFormatter,
        
        // 手机号列
        mobileFormatter: tableFormatters.mobileFormatter,
        
        // 身份证列
        idCardFormatter: tableFormatters.idCardFormatter,
        
        // 通用敏感信息formatter
        genericSensitiveFormatter: function(val, obj, row, act) {
            // 这里可以根据列名自动判断脱敏类型
            var columnName = this.name || ''; // 假设可以获取到列名
            return tableFormatters.sensitiveFormatter(val, obj, row, act, columnName);
        }
    };
}

// 工具函数：检查用户权限
function checkUserPermission() {
    // 这里应该根据实际的权限系统来实现
    // 示例：从后台获取用户权限
    return new Promise(function(resolve) {
        $.ajax({
            url: '/api/user/permission',
            method: 'GET',
            success: function(response) {
                var canViewSensitiveData = response.permissions && 
                    response.permissions.includes('VIEW_SENSITIVE_DATA');
                resolve(!canViewSensitiveData); // 如果没有权限则需要脱敏
            },
            error: function() {
                resolve(true); // 出错时默认脱敏
            }
        });
    });
}

// 初始化函数
function initDataMaskUsage() {
    // 检查用户权限并设置脱敏状态
    checkUserPermission().then(function(shouldMask) {
        DataMaskConfig.globalEnabled = shouldMask;
        
        // 重新初始化脱敏配置
        initDataMask();
        
        console.log('数据脱敏初始化完成，状态：', shouldMask ? '启用' : '禁用');
    });
}

// 暴露到全局
window.DataMaskUsage = {
    createTableWithMask: createTableWithMask,
    maskDataObject: maskDataObject,
    handleAjaxResponse: handleAjaxResponse,
    extendTableConfig: extendTableConfig,
    createMaskToggle: createMaskToggle,
    batchMaskTableData: batchMaskTableData,
    integrateWithRentalHouseList: integrateWithRentalHouseList,
    checkUserPermission: checkUserPermission,
    init: initDataMaskUsage
};

// 自动初始化
$(document).ready(function() {
    initDataMaskUsage();
});
