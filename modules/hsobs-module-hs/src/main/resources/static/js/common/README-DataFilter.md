# 数据脱敏过滤器使用说明

## 概述

数据脱敏过滤器是一个用于前端数据展示的统一脱敏解决方案，支持对身份证号、手机号、姓名、邮箱、地址等敏感信息进行脱敏处理。

## 文件结构

```
js/common/
├── dataFilter.js              # 核心脱敏过滤器
├── dataFilter-config.js       # 脱敏配置文件
├── dataFilter-usage.js        # 使用示例
├── table-mask-helper.js       # 表格脱敏助手
└── README-DataFilter.md       # 使用说明
```

## 快速开始

### 1. 引入文件

在HTML页面中引入相关JS文件：

```html
<script src="${ctxStatic}/js/common/dataFilter.js"></script>
<script src="${ctxStatic}/js/common/dataFilter-config.js"></script>
<script src="${ctxStatic}/js/common/table-mask-helper.js"></script>
```

### 2. 基本使用

#### 在表格formatter中使用

```javascript
// 方式1：使用预定义的脱敏类型
{
    header: '姓名',
    name: 'userName',
    formatter: TableMaskHelper.createMaskFormatter('name')
}

// 方式2：自动检测字段类型
{
    header: '手机号',
    name: 'mobile',
    formatter: TableMaskHelper.createAutoMaskFormatter('mobile')
}

// 方式3：手动判断
{
    header: '身份证',
    name: 'idCard',
    formatter: function(val, obj, row, act) {
        if (shouldEnableMask()) {
            return TableMaskHelper.mask.idCard(val);
        }
        return val;
    }
}
```

#### 对整个数据对象脱敏

```javascript
// 脱敏单个对象
var maskedData = DataFilter.filterData(originalData, shouldEnableMask());

// 脱敏数组数据
var maskedList = DataFilter.filterData(dataList, shouldEnableMask());
```

## 脱敏规则

### 支持的数据类型

| 类型 | 脱敏规则 | 示例 |
|------|----------|------|
| 身份证号 | 保留前6位和后4位 | 110101********1234 |
| 手机号 | 保留前3位和后4位 | 138****5678 |
| 银行卡号 | 保留前4位和后4位 | 6222****1234 |
| 姓名 | 保留姓氏，名字用*替换 | 张** |
| 邮箱 | 保留前3位和@后域名 | abc***@example.com |
| 地址 | 保留前6位，后面用*替换 | 北京市朝阳区****** |

### 字段名匹配规则

系统会根据字段名自动判断脱敏类型：

- `idCard`, `identityCard`, `sfzh` → 身份证脱敏
- `mobile`, `phone`, `sjh` → 手机号脱敏
- `bankCard`, `cardNo`, `yhkh` → 银行卡脱敏
- `userName`, `realName`, `name`, `xm` → 姓名脱敏
- `email`, `mail` → 邮箱脱敏
- `address`, `addr`, `dz` → 地址脱敏

## 配置说明

### 全局配置

在 `dataFilter-config.js` 中可以配置：

```javascript
// 全局启用状态
DataMaskConfig.globalEnabled = true;

// 页面级别配置
DataMaskConfig.pageConfigs = {
    '/user/': { enabled: true },
    '/house/': { enabled: true }
};

// 角色权限配置
DataMaskConfig.roleConfigs = {
    'admin': { enabled: false },  // 管理员不脱敏
    'user': { enabled: true }     // 普通用户脱敏
};
```

### 自定义脱敏规则

```javascript
// 添加新的脱敏规则
DataFilter.config.customField = {
    pattern: /^(\d{2})\d+(\d{2})$/,
    replacement: '$1****$2'
};

// 添加新的敏感字段
DataFilter.sensitiveFields.push(/customField/i);
```

## 权限控制

### 基于用户角色

```javascript
// 检查用户权限
function checkUserPermission() {
    return new Promise(function(resolve) {
        $.ajax({
            url: '/api/user/permission',
            success: function(response) {
                var canView = response.permissions.includes('VIEW_SENSITIVE_DATA');
                resolve(!canView); // 没有权限则需要脱敏
            }
        });
    });
}
```

### 动态控制

```javascript
// 动态启用/禁用脱敏
DataMaskConfig.globalEnabled = true;  // 启用
DataMaskConfig.globalEnabled = false; // 禁用

// 重新初始化
initDataMask();
```

## 实际应用示例

### 在现有表格中集成

```javascript
// 原有的表格配置
var columns = [
    {header: '房屋编号', name: 'houseCode'},
    {header: '业主姓名', name: 'ownerName'},
    {header: '联系电话', name: 'mobile'},
    {header: '身份证号', name: 'idCard'}
];

// 添加脱敏功能
columns = TableMaskHelper.addMaskToColumns(columns);

// 或者单独为敏感字段添加脱敏
columns[1].formatter = TableMaskHelper.createMaskFormatter('name');
columns[2].formatter = TableMaskHelper.createMaskFormatter('mobile');
columns[3].formatter = TableMaskHelper.createMaskFormatter('idCard');
```

### Ajax数据处理

```javascript
$.ajax({
    url: '/api/data',
    success: function(response) {
        // 对返回数据进行脱敏处理
        var maskedData = DataFilter.filterData(response.data, shouldEnableMask());
        
        // 使用脱敏后的数据
        updateTable(maskedData);
    }
});
```

## 调试和测试

### 启用调试模式

```javascript
// 在控制台中查看脱敏状态
console.log('脱敏启用状态:', shouldEnableMask());

// 测试脱敏效果
console.log('姓名脱敏:', TableMaskHelper.mask.name('张三'));
console.log('手机脱敏:', TableMaskHelper.mask.mobile('13812345678'));
```

### 测试用例

```javascript
// 测试不同类型的脱敏
var testData = {
    userName: '张三',
    mobile: '13812345678',
    idCard: '11010119900101123X',
    email: '<EMAIL>'
};

var masked = DataFilter.filterData(testData, true);
console.log('脱敏结果:', masked);
```

## 注意事项

1. **性能考虑**：脱敏处理会增加一定的计算开销，建议在数据量大的场景下进行性能测试
2. **安全性**：前端脱敏只是展示层面的处理，敏感数据的真正保护应该在后端实现
3. **兼容性**：确保在目标浏览器中测试脱敏功能的兼容性
4. **用户体验**：提供脱敏开关功能，让有权限的用户可以查看完整数据

## 常见问题

### Q: 如何添加新的脱敏类型？
A: 在 `DataFilter.config` 中添加新的规则，并在 `sensitiveFields` 中添加字段匹配规则。

### Q: 如何禁用某个字段的脱敏？
A: 在字段的 `formatter` 中直接返回原值，不调用脱敏方法。

### Q: 如何根据用户权限动态控制脱敏？
A: 实现 `shouldEnableMask()` 函数，根据用户权限返回 true/false。
