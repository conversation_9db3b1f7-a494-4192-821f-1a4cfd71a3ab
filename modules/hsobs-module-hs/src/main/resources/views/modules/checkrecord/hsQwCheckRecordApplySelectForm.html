<% layout('/layouts/default.html', {title: '资格核查申请', libs: ['validate','fileupload','dataGrid']}){ %>
<div class="main-content">
    <div class="box box-main">
        <div class="box-header with-border">
            <div class="box-title">
                <i class="fa icon-note"></i> ${text('资格核查申请')}
            </div>
            <div class="box-tools pull-right hide">
                <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
            </div>
        </div>
        <#form:form id="inputForm" model="${hsQwCheckRecord}" action="${ctx}/checkrecord/hsQwCheckRecord/recordSave" method="post" class="form-horizontal">
        <div class="box-body hs-box-body-bpm">
<!--            <div class="form-unit">${text('资格核查信息')}</div>-->
<!--            <div class="hs-table-div" >-->
<!--                <table class="table-form hs-table-form" >-->
<!--                    <tr>-->
<!--                        <td class="form-label hs-form-label">-->
<!--                            <span class="required ">*</span> ${text('核查时间')}：<i class="fa icon-question hide"></i>-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            <#form:input path="checkDate" maxlength="20" class="form-control laydate required"-->
<!--                            dataFormat="date" data-type="date" data-format="yyyy-MM-dd" />-->
<!--                        </td>-->
<!--                        <td class="form-label hs-form-label">-->
<!--                            <span class="required ">*</span> ${text('核查类型')}：<i class="fa icon-question hide"></i>-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            <#form:select path="violationType" dictType="hs_qw_check_violation_type" class="form-control required" />-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                </table>-->
<!--            </div>-->
            <div class="form-unit">${text('核查用户')}</div>
            <div class="form-unit-wrap table-form">
                <#hs:applyhouseSelect multiple="true" path="hsQwApplyerList"
                value="${toJson(hsQwCheckRecord.hsQwApplyList)}"
                title="选择配租用户" dataUrl="/house/hsQwPublicRentalHouse/houseSelectByType?dataType=7"/>
            </div>
            <div class="form-unit">${text('核查事项')}</div>
            <div class="form-unit-wrap table-form">
                <#hs:checkObjectSelect multiple="true" path="hsObjectList" value="${toJson(hsQwCheckRecord.hsObjectList)}" />
            </div>
        </div>
        <div class="box-footer">
            <div class="row">
                <div class="col-sm-offset-5 col-sm-10">
                    <button type="submit" class="btn btn-sm btn-primary" id="btnSubmit"><i class="fa fa-check"></i> ${text('提 交')}</button>&nbsp;
                    <button type="button" class="btn btn-sm btn-default" id="btnCancel" onclick="js.closeCurrentTabPage()"><i class="fa fa-reply-all"></i> ${text('关 闭')}</button>
                </div>
            </div>
        </div>
    </#form:form>
</div>
</div>
<% } %>
<script>
    $('#inputForm').validate({
        submitHandler: function(form){
            // 验证是否选择了房源
            var houseIds = $('#houseIdStr').val();
            if (!houseIds || houseIds.trim() === '') {
                js.showMessage('请选择至少一个配租用户');
                return false;
            }
            
            // 验证是否选择了核查事项
            var objectIds = $('#objectIdStr').val();
            if (!objectIds || objectIds.trim() === '') {
                js.showMessage('请选择至少一个核查事项');
                return false;
            }
            
            js.ajaxSubmitForm($(form), function(data){
                js.showMessage(data.message);
                if(data.result == Global.TRUE){
                    js.closeCurrentTabPage(function(contentWindow){
                        contentWindow.page();
                    });
                }
            }, "json");
        }
    });
    // 业务实现公示按钮
    $('#btnSubmit1').click(function(){
        console.log("erewrweqrewr")
        $('#status').val('1');
    });
    // 业务实现草稿按钮
    $('#btnSubmit2').click(function(){
        $('#submitType').val('0');
    });
    // 业务实现草稿按钮
    $('#btnSubmit3').click(function(){
        $('#submitType').val('1');
    });
</script>