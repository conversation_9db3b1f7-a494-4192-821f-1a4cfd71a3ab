<% layout('/layouts/default.html', {title: text(title!), libs: ['dataGrid']}){ %>
<!-- 引入数据脱敏过滤器 -->
<script src="${ctxStatic}/js/common/dataFilter.js"></script>
<script src="${ctxStatic}/js/common/dataFilter-config.js"></script>
<script src="${ctxStatic}/js/common/dataFilter-usage.js"></script>
<!-- 引入公共表格样式 -->
<div class="main-content">
    <div class="box box-main">
        <div class="box-header">
            <%
            var houseTypeStr = "";
            if (hsQwPublicRentalHouse != null) {
                if (hsQwPublicRentalHouse.type == "0") {
                    houseTypeStr = "公租房";
                } else if (hsQwPublicRentalHouse.type == "1") {
                    houseTypeStr = "限价房";
                } else if (hsQwPublicRentalHouse.type == "2") {
                    houseTypeStr = "公用住房";
                } else if (hsQwPublicRentalHouse.type == "3") {
                    houseTypeStr = "局直公房";
                } else if (hsQwPublicRentalHouse.type == "4") {
                    houseTypeStr = "自管公房";
                }
            }
            %>
            <div class="box-title">
                <i class="fa icon-notebook"></i> ${text(houseTypeStr)}${text('房源管理')}
            </div>
            <div class="box-tools pull-right">
                <a href="#" class="btn btn-default" id="btnSearch" title="${text('查询')}"><i class="fa fa-filter"></i> ${text('查询')}</a>
<!--              <a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>-->
                <% if(hasPermi('house:hsQwPublicRentalHouse:edit')){ %>
                <a href="#" class="btn btn-default" id="btnExport"><i class="glyphicon glyphicon-export"></i> 导出</a>
                <a href="#" class="btn btn-default" id="btnImport"><i class="glyphicon glyphicon-import"></i> 导入</a>
                    <a href="${ctx}/house/hsQwPublicRentalHouse/form?type=${hsQwPublicRentalHouse.type}" class="btn btn-default btnTool" title="${text('新增')}${text(houseTypeStr)}"><i class="fa fa-plus"></i> ${text('新增')}</a>
                    <button type="button" id="publicBtn" class="btn btn-default" onclick="return false;"><i class="fa fa-plus"></i> ${text('一键发布')}</button>
                <% } %>
                <a href="#" class="btn btn-default" id="btnSetting" title="${text('设置')}"><i class="fa fa-navicon"></i></a>
            </div>
        </div>
        <div class="box-body">
            <div class="search-form-container">
                <#form:form id="searchForm" model="${hsQwPublicRentalHouse}" action="${ctx}/house/hsQwPublicRentalHouse/listData" method="post" class="form-inline hide"
                        data-page-no="${parameter.pageNo}" data-page-size="${parameter.pageSize}" data-order-by="${parameter.orderBy}">

                    <!-- 第一行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('楼盘名称')}：</label>
                            <div class="control-inline">
                                <#form:listselect id="houseId" title="楼盘选择"
                                path="estateId"
                                url="${ctx}/estate/hsQwPublicRentalEstate/hsQwPublicRentalEstateSelect" allowClear="false"
                                checkbox="false" itemCode="id" itemName="name" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('楼号')}：</label>
                            <div class="control-inline">
                                <#form:input path="buildingNum" maxlength="10" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('单元号')}：</label>
                            <div class="control-inline">
                                <#form:input path="unitNum" maxlength="10" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>

                    <!-- 第二行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('VR房源')}：</label>
                            <div class="control-inline">
                                <#form:listselect id="vrInfoSelect" title="VR信息检索"
                                path="hsQwHouseVr.id"
                                url="${ctx}/housrvr/hsQwHouseVr/hsQwHouseVrSelect" allowClear="false"
                                checkbox="false" itemCode="id" itemName="estateName" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('户型')}：</label>
                            <div class="control-inline">
                                <#form:select path="houseType" dictType="hs_house_house_type" blankOption="true" class="form-control width-120"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('是否发布')}：</label>
                            <div class="control-inline">
                                <#form:select path="isPublic" dictType="hs_house_public" blankOption="true" class="form-control width-120"/>
                            </div>
                        </div>
                    </div>

                    <!-- 第三行：3个查询条件 -->
                    <div class="search-form-row">
                        <div class="form-group">
                            <label class="control-label">${text('创建时间')}：</label>
                            <div class="control-inline">
                                <#form:input path="createDate_gte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                                    dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm" data-done="createDate_lte.click()"/>
                                &nbsp;-&nbsp;
                                <#form:input path="createDate_lte" readonly="true" maxlength="20" class="form-control laydate width-datetime"
                                    dataFormat="datetime" data-type="datetime" data-format="yyyy-MM-dd HH:mm"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('所属单位')}：</label>
                            <div class="control-inline">
                                <#form:treeselect id="publicOrg" title="${text('机构选择')}"
                                path="officeCode" labelPath=""
                                url="${ctx}/sys/office/treeData" allowClear="true"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label">${text('房屋状态')}：</label>
                            <div class="control-inline">
                                <% if(hsQwPublicRentalHouse.type == "1" || hsQwPublicRentalHouse.type == "2"){ %>
                                <#form:select path="houseStatus" dictType="hs_public_house_status" blankOption="true" class="form-control width-120"/>
                                <% } else { %>
                                <#form:select path="houseStatus" dictType="hs_rent_house_status" blankOption="true" class="form-control width-120"/>
                                <% } %>
                            </div>
                        </div>
                    </div>

                    <#form:hidden path="type"/>

                    <!-- 按钮行 -->
                    <div class="search-button-row">
                        <button type="submit" class="btn btn-primary btn-sm"><i class="glyphicon glyphicon-search"></i> ${text('查询')}</button>
                        <button type="reset" class="btn btn-default btn-sm isQuick"><i class="glyphicon glyphicon-repeat"></i> ${text('重置')}</button>
                    </div>
                </#form:form>
            </div>

            <div class="fixed-table-container">
                <table id="dataGrid"></table>
                <div id="dataGridPage"></div>
            </div>
        </div>
    </div>
</div>
<% } %>
<script>
//# // 初始化DataGrid对象
$('#dataGrid').dataGrid({
	searchForm: $('#searchForm'),
	showCheckbox: true,
	shrinkToFit: false, // 禁用自动调整列宽
	autowidth: false, // 禁用自动宽度
	//scroll: true, // 启用滚动条
	scrollOffset: 18,
	width: '100%', // 表格宽度
	height: 'auto', // 表格高度自适应
	columnModel: [
        {header:'${text("住房编号")}', name:'id',  sortable:false, width:160, align:"left"},
		{header:'${text("楼盘名称")}', name:'estate.name',  sortable:false, width:180, align:"left", formatter: function(val, obj, row, act){
				return '<a href="${ctx}/estate/hsQwPublicRentalEstate/form?id='+row.estateId+'" class="hsBtnList" data-title="${text("查看楼盘信息")}">'+(val||row.estateId)+'</a>';
		}},
		{header:'${text("楼盘位置")}', name:'estate.address',  sortable:false, width:180, align:"left"},
		{header:'${text("楼号")}', name:'buildingNum',  sortable:false, width:100, align:"left"},
		{header:'${text("单元号")}', name:'unitNum',  sortable:false, width:100, align:"left"},
		{header:'${text("楼层")}', name:'floor',  sortable:false, width:100, align:"left"},
		{header:'${text("建筑面积")}', name:'buildingArea',  sortable:false, width:100, align:"left"},
		{header:'${text("所属单位")}', name:'office.officeName',  sortable:false, width:150, align:"left"},
		{header:'${text("VR信息")}', name:'hsQwHouseVr.vrUrl',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
			// console.log('isInternt:', '${isInternt}');
			// console.log('val:', val);
			// console.log('obj:', obj);
			// console.log('row:', row);

			var vrUrl = '';
			// 优先从row对象获取数据，如果没有则从obj获取，最后使用val
			var vrData = (row && row.hsQwHouseVr) || (obj && obj.hsQwHouseVr) || {};

			if ('${isInternt}' === '0') {
				vrUrl = vrData.vrUrl || val || '';
			} else if ('${isInternt}' === '1') {
				vrUrl = vrData.vrInternetUrl || '';
			}

			if (vrUrl && vrUrl.trim() !== '') {
				return '<a href="' + vrUrl + '" target="_blank" class="btn btn-link btn-xs"><i class="fa fa-external-link"></i> ${text("查看VR")}</a>';
			} else {
				return '${text("无")}';
			}
		}},
		{header:'${text("户型")}', name:'houseType',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_house_type')}", val, '${text("未知")}', true);
			}},
		{header:'${text("房屋状态")}', name:'houseStatus',  sortable:false, width:150, align:"left", formatter: function(val, obj, row, act){
			return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_status')}", val, '${text("未知")}', true);
		}},
		<% if(hsQwPublicRentalHouse.type=='3' || hsQwPublicRentalHouse.type=='4') { %>
        {header:'${text("核验状态")}', name:'bureau.autoCheck',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
            return js.getDictLabel("#{@DictUtils.getDictListJson('hs_qw_house_auto_check')}", val, '${text("未知")}', true);
        }},
        {header:'${text("核验时间")}', name:'bureau.checkDate',  sortable:false, width:150, align:"left"},
		<% } %>
		{header:'${text("是否发布")}', name:'isPublic',  sortable:false, width:100, align:"left", formatter: function(val, obj, row, act){
				return js.getDictLabel("#{@DictUtils.getDictListJson('hs_house_public')}", val, '${text("未知")}', true);
			}},
		{header:'${text("创建时间")}', name:'createDate',  sortable:false, width:150, align:"left"},
		{header:'${text("操作")}', name:'actions', align:"left", width:150, formatter: function(val, obj, row, act){
			var actions = [];
			//# if(hasPermi('house:hsQwPublicRentalHouse:edit')){
				actions.push('<a href="${ctx}/house/hsQwPublicRentalHouse/form?id='+row.id+'" class="hsBtnList" title="${text("编辑")}">编辑</a>&nbsp;');
				actions.push('<a href="${ctx}/house/hsQwPublicRentalHouse/delete?id='+row.id+'" class="btnList" title="${text("删除")}" data-confirm="${text("确认要删除该房源信息吗？")}">删除</a>&nbsp;');
			//# }
			// 添加外层条件判断，仅当 hsQwPublicRentalHouse.type 为 "3" 或 "4" 时展示按钮
			if (['3'].includes('${hsQwPublicRentalHouse.type}')) {
			    if (row.bureau!=null && (row.bureau.status == '0' || row.bureau.status == '4')){
			        actions.push('<a href="${ctx}/bureau/hsQwApplyBureau/clear?id='+row.bureau.id+'" class="btnList" title="${text("清退")}" data-confirm="${text("确认要执行腾退操作吗？")}">腾退</a>&nbsp;');
			    }
			    actions.push('<a href="${ctx}/bureau/hsQwApplyBureau/listBureau?houseId='+row.id+'" class="btnList" title="${text("历史承租信息")}" >历史承租</a>&nbsp;');
			}
            if (['4'].includes('${hsQwPublicRentalHouse.type}')) {
                if (row.bureau!=null && (row.bureau.status == '0' || row.bureau.status == '4')){
                    actions.push('<a href="${ctx}/province/hsQwApplyProvince/clear?id='+row.province.id+'" class="btnList" title="${text("清退")}" data-confirm="${text("确认要执行腾退操作吗？")}"><i class="fa fa-ban"></i></a>&nbsp;');
                }
            }
			return actions.join('');
		}}
	],
    ajaxSuccess: function() {
		            requestAnimationFrame(function() {
                CommonTable.setupFixedColumns('dataGrid');
            });
		// js.closeLoading(0, true);
	}
});
</script>


<script>
$('#btnExport').click(function(){
    var hids = $('#dataGrid').dataGrid('getSelectRows');
    if (hids != null && hids.length > 0){
        js.confirm('${text("是否导出数据？")}', function(){

            js.ajaxSubmitForm($('#searchForm'), {
                url: '${ctx}/house/hsQwPublicRentalHouse/exportData2',
                data: { houseStr: hids.join(',') },
                clearParams: 'pageNo,pageSize',
                downloadFile: true
            });
        });
    }else{
        js.showMessage('${text("请在列表选中要导出的房源")}');
    }
    return false;
});
$('#publicBtn').click(function(){
	var hids = $('#dataGrid').dataGrid('getSelectRows');
	if (hids != null && hids.length > 0){
		js.confirm('${text("确认要对选中的房源进行发布吗？")}', function(){
			js.ajaxSubmit('${ctx}/house/hsQwPublicRentalHouse/public', {
				houseStr: hids.join(',')
			}, function(data){
				js.showMessage(data.message);
				page();
			});
		});
	}else{
		js.showMessage('${text("请在列表选中要进行发布的房源")}');
	}
	return false;
});
$('#btnImport').click(function(){
	js.layer.open({
		type: 1,
		area: ['400px'],
		title: '${text("导入房源房源信息表")}',
		resize: false,
		scrollbar: true,
		content: js.template('importTpl'),
		btn: ['<i class="fa fa-check"></i> ${text("导入")}',
			'<i class="fa fa-remove"></i> ${text("关闭")}'],
		btn1: function(index, layero){
			var form = {
				inputForm: layero.find('#inputForm'),
				file: layero.find('#file').val(),
				type: "0"
			};
		    if (form.file == '' || (!js.endWith(form.file, '.xls') && !js.endWith(form.file, '.xlsx'))){
		    	js.showMessage("${text('文件不正确，请选择后缀为xls或xlsx的文件。')}", null, 'warning');
		        return false;
		    }
			js.ajaxSubmitForm(form.inputForm, function(data){
				js.showMessage(data.message);
				if(data.result == Global.TRUE){
					js.layer.closeAll();
				}
				page();
			}, "json");
			return true;
		}
	});
});
</script>
<script id="importTpl" type="text/template">//<!--
<form id="inputForm" action="${ctx}/house/hsQwPublicRentalHouse/importData" method="post" enctype="multipart/form-data"
	class="form-horizontal mt20 mb10" style="overflow:auto;max-height:200px;">
	<div class="row">
		<div class="col-xs-12 col-xs-offset-1">
			<input type="file" id="file" name="file" class="form-file"/>
			<input type="hidden" id="type" name="type" value="${hsQwPublicRentalHouse.type}"/>
			<div class="mt10 pt5" style="color:red">
				${text('提示：仅允许导入"xls"或"xlsx"格式文件！')}
			</div>
			<div class="mt10 pt5">
				<a href="${ctx}/house/hsQwPublicRentalHouse/importTemplate" class="btn btn-default btn-xs"><i class="fa fa-file-excel-o"></i> ${text('下载模板')}</a>
			</div>
		</div>
	</div>
</form>
//--></script>