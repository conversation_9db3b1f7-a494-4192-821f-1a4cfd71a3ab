package com.hsobs.hs.modules.house.entity;

import com.jeesite.common.annotation.DataMask;
import com.jeesite.common.entity.DataEntity;
import com.jeesite.common.mybatis.annotation.Column;
import com.jeesite.common.mybatis.annotation.Table;
import com.jeesite.common.mybatis.mapper.query.QueryType;

/**
 * 房屋业主信息Entity
 * 演示数据脱敏功能的使用
 * 
 * <AUTHOR>
 * @version 2024-01-01
 */
@Table(name = "hs_qw_house_owner", alias = "a", label = "房屋业主信息", columns = {
    @Column(name = "id", attrName = "id", label = "编号", isPK = true),
    @Column(name = "owner_name", attrName = "ownerName", label = "业主姓名", queryType = QueryType.LIKE),
    @Column(name = "mobile", attrName = "mobile", label = "手机号码"),
    @Column(name = "id_card", attrName = "idCard", label = "身份证号"),
    @Column(name = "email", attrName = "email", label = "邮箱"),
    @Column(name = "address", attrName = "address", label = "联系地址"),
    @Column(name = "bank_card", attrName = "bankCard", label = "银行卡号"),
})
public class HsQwHouseOwner extends DataEntity<HsQwHouseOwner> {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 业主姓名 - 使用姓名脱敏
     */
    @DataMask(type = DataMask.MaskType.NAME)
    private String ownerName;
    
    /**
     * 手机号码 - 使用手机号脱敏
     */
    @DataMask(type = DataMask.MaskType.MOBILE)
    private String mobile;
    
    /**
     * 身份证号 - 使用身份证脱敏，并且只有在用户没有查看敏感数据权限时才脱敏
     */
    @DataMask(
        type = DataMask.MaskType.ID_CARD,
        condition = "!@hasPermission.hasPermission('house:owner:viewIdCard')"
    )
    private String idCard;
    
    /**
     * 邮箱 - 使用邮箱脱敏
     */
    @DataMask(type = DataMask.MaskType.EMAIL)
    private String email;
    
    /**
     * 联系地址 - 使用地址脱敏
     */
    @DataMask(type = DataMask.MaskType.ADDRESS)
    private String address;
    
    /**
     * 银行卡号 - 使用自定义脱敏规则
     */
    @DataMask(
        type = DataMask.MaskType.CUSTOM,
        pattern = "(\\d{4})\\d+(\\d{4})",
        replacement = "$1****$2"
    )
    private String bankCard;
    
    /**
     * 备注信息 - 不脱敏
     */
    private String remarks;
    
    public HsQwHouseOwner() {
        this(null);
    }
    
    public HsQwHouseOwner(String id) {
        super(id);
    }
    
    // Getter和Setter方法
    
    public String getOwnerName() {
        return ownerName;
    }
    
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
    
    public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    
    public String getIdCard() {
        return idCard;
    }
    
    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getBankCard() {
        return bankCard;
    }
    
    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }
    
    public String getRemarks() {
        return remarks;
    }
    
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
