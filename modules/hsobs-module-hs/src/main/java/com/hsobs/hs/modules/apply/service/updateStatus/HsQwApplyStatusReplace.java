package com.hsobs.hs.modules.apply.service.updateStatus;

import com.hsobs.hs.modules.apply.entity.HsQwApply;
import com.hsobs.hs.modules.apply.service.HsQwApplyService;
import com.hsobs.hs.modules.compact.entity.HsQwCompact;
import com.hsobs.hs.modules.compact.service.HsQwCompactService;
import com.jeesite.common.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class HsQwApplyStatusReplace extends HsQwApplyStatusBase implements HsQwApplyStatus {

    @Override
    public String getApplyMater() {
        return "4";
    }

    @Override
    public void execute(HsQwApply newHsQwApply, HsQwApply hsQwApply, HsQwApplyService hsQwApplyService) {
        HsQwApply oldHsQwApply = new HsQwApply();
        //执行清退
        if (hsQwApply.isClear()){
            hsQwApplyService.invalidApplyInfo(newHsQwApply);
        } else {//状态更新
            hsQwApplyService.realUpdateStatus(hsQwApply);
            if (hsQwApply.getStatus().equals(HsQwApply.STATUS_NORMAL)) {
                // 流程正常结束
                /*****************旧申请单信息归档备份********************/
                //获取旧订单信息
                oldHsQwApply = hsQwApplyService.getBase(newHsQwApply.getApplyedId());
                super.saveToApplyHis(oldHsQwApply);

                /*****************
                 • 旧房源状态：恢复待配租
                 • 新房源状态：设置已配租
                 ********************/
                super.updateHouseStatus(oldHsQwApply.getHouseId(),"0", hsQwApplyService);
                super.updateHouseStatus(newHsQwApply.getHouseId(),"1", hsQwApplyService);
                oldHsQwApply.setHouseId(newHsQwApply.getHouseId());
                hsQwApplyService.update(oldHsQwApply);

                /*****************
                 • 旧合同：删除
                 • 新合同：拷贝并关联新合同
                 ********************/
                super.copyAndInvalidCompact(oldHsQwApply, newHsQwApply);
            } else {
                // 流程异常结束
                /*****************恢复房源为待配租********************/
                if (newHsQwApply.getHouseId()!=null &&
                        !newHsQwApply.getHouseId().equals(oldHsQwApply.getHouseId())) {
                    super.updateHouseStatus(newHsQwApply.getHouseId(), "0", hsQwApplyService);//流程异常，恢复待配租
                }

                /*****************合同失效********************/
                if (newHsQwApply.getCompact()!=null){
                    newHsQwApply.getCompact().setStatus(HsQwCompact.STATUS_DISABLE);//流程异常，失效合同
                    hsQwCompactService.updateStatus(newHsQwApply.getCompact());
                }
            }
        }
    }
}
