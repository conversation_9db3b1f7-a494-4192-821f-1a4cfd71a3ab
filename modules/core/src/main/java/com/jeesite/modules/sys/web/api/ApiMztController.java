/**
 * Copyright (c) 2013-Now http://jeesite.com All rights reserved.
 * No deletion without permission, or be held responsible to law.
 */
package com.jeesite.modules.sys.web.api;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.cache.CacheUtils;
import com.jeesite.common.config.Global;
import com.jeesite.common.io.FileUtils;
import com.jeesite.common.lang.DateUtils;
import com.jeesite.common.service.ServiceException;
import com.jeesite.common.shiro.session.SessionDAO;
import com.jeesite.common.web.BaseController;
import com.jeesite.modules.file.entity.FileUpload;
import com.jeesite.modules.file.entity.FileUploadParams;
import com.jeesite.modules.file.service.FileUploadService;
import com.jeesite.modules.sys.entity.EmpUser;
import com.jeesite.modules.sys.entity.Employee;
import com.jeesite.modules.sys.entity.api.*;
import com.jeesite.modules.sys.service.EmpUserService;
import com.jeesite.modules.sys.service.EmployeeService;
import com.jeesite.modules.sys.utils.EmpUtils;
import com.jeesite.modules.sys.utils.SM2Utils;
import com.jeesite.modules.sys.utils.UserUtils;
import io.swagger.annotations.Api;
import lombok.Synchronized;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.*;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 单点登录Controller
 *
 * <AUTHOR>
 * @version 2020-9-19
 */
@Controller
@Api(tags = "API - API接口")
@ConditionalOnProperty(name = {"user.enabled", "web.core.enabled"}, havingValue = "true", matchIfMissing = true)
public class ApiMztController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ApiMztController.class);

    private static final ConcurrentHashMap<String, Object> phoneLocks = new ConcurrentHashMap<>();

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    FileUploadService fileUploadService;

    @Autowired
    EmpUserService empUserService;

    @Autowired
    EmployeeService employeeService;

    @Autowired
    private SessionDAO sessionDAO;

    @ResponseBody
    @RequestMapping(value = "api/token")
    public ApiResponseBody ssoLogin(@RequestBody ApiTokenBody body) {
        logger.debug("MZT认证请求报文信息：" + body.toString());
        String accountId = body.getAccountId();
        if (!Global.getConfig("mzt.accountId").toString().equals(accountId)) {
            return ApiResponseBody.error("用户不存在！");
        }
        if (!SM2Utils.SM2SigVerifier(accountId, body.getSign(), Global.getConfig("mzt.sm2.pbk"))) {
            return ApiResponseBody.error("平台认证失败！");
        }
        String accessToken = this.getMztAccessToken(accountId);
        Map loginInfo = new HashMap<>();
        loginInfo.put("accessToken", accessToken);
        loginInfo.put("expiresIn", Global.getConfig("mzt.expiresIn", "3600"));
        logger.debug("MZT认证返回报文信息：" + ApiResponseBody.sucess(loginInfo).toString());
        return ApiResponseBody.sucess(loginInfo);
    }

    private String getMztAccessToken(String accountId) {
        String accessToken = CacheUtils.get("mzt_token", accountId);
        if (accessToken == null) {
            accessToken = UUID.randomUUID().toString();//生成随机token
            CacheUtils.put("mzt_token", accountId, accessToken, Long.parseLong(Global.getConfig("mzt.expiresIn", "3600")));
        }
        return accessToken;
    }

    ObjectMapper objectMapper = new ObjectMapper();

    @ResponseBody
    @RequestMapping(value = "api/**")
    public ApiResponseBody request(@RequestBody ApiRequestBody<Map> body, HttpServletRequest request) {

        try {
            logger.debug("→ MZT业务请求接收: {}", body.toString());

            // 1. 检查 accessToken
            this.checkAccessToken(body.getHead());

            // 2. 构建请求地址
            String fullPath = request.getRequestURI().substring(request.getContextPath().length() + 4);
            String exchangeUrl = "http://127.0.0.1:" + Global.getProperty("server.port")
                    + FileUtils.path("/" + Global.getProperty("server.servlet.context-path"))
                    + "/admin" + fullPath;

            // 3. 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String sessionId = this.getSessionByPhone(body.getData());
            headers.add("Cookie", "jeesite.session.id=" + sessionId);

            // 4. 请求体序列化为 JSON 字符串用于日志
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            String requestJson = objectMapper.writeValueAsString(body.getData());

            logger.info("→ 对接请求地址: {}", exchangeUrl);
            logger.info("→ 请求头: {}", headers);
            logger.info("→ 请求参数: {}", requestJson);

            HttpEntity<Map> requestEntity = new HttpEntity<>(body.getData(), headers);

            String response;
            try {
                // 5. 发送请求并接收响应
                ResponseEntity<String> responseEntity = restTemplate.exchange(
                        exchangeUrl,
                        HttpMethod.POST,
                        requestEntity,
                        String.class
                );
                response = responseEntity.getBody();

                logger.info("← 原始响应: {}", response);

                if (response == null) {
                    return ApiResponseBody.sucessMap();
                }

                this.checkErrorResp(response);

            } catch (ServiceException e) {
                logger.error("× 接口业务异常: {}", e.getMessage(), e);
                return ApiResponseBody.error("接口请求失败，请核查！" + this.getMessage(e.getMessage()));
            } catch (HttpServerErrorException.InternalServerError e) {
                logger.error("× 接口服务器500异常: {}", e.getMessage(), e);
                return ApiResponseBody.error("接口请求失败，请核查！" + this.getMessage(e.getMessage()));
            } catch (Exception e) {
                logger.error("× 接口请求异常: {}", e.getMessage(), e);
                return ApiResponseBody.error("接口请求失败，请核查！" + this.getMessage(e.getMessage()));
            }

            // 6. 替换字段
            String replaceStr = response.replaceAll("\"count\"", "\"total\"")
                    .replaceAll("\"list\"", "\"dataList\"");

            Map<String, Object> responseMap = objectMapper.readValue(replaceStr, Map.class);
            ApiResponseBody result = ApiResponseBody.sucess(responseMap);

            logger.info("← 格式化响应结果: {}", objectMapper.writeValueAsString(result));
            return result;

        } catch (ServiceException | JsonProcessingException e) {
            logger.error("× 请求处理异常: {}", e.getMessage(), e);
            return ApiResponseBody.error(e.getMessage());
        }
    }



    private String getMessage(String response) {
        logger.debug("内部接口响应数据：" + response.toString());

        // 正则表达式匹配 message 字段的值
        Pattern pattern = Pattern.compile("\"message\"\\s*:\\s*\"(.*?)\"");
        Matcher matcher = pattern.matcher(response);

        if (matcher.find()) {
            String message = matcher.group(1);  // 提取捕获的内容
            return message;
        } else {
            return "系统处理异常！";
        }

    }

    private void checkErrorResp(String response) {
        logger.debug("内部接口响应数据：" + response.toString());
        JSONObject jsonObject = JSONObject.parseObject(response);
        Object result  = jsonObject.get("result");
        if (StringUtils.isEmpty(result) || result.toString().equals("true")){
            return;
        }
        throw new ServiceException(response);
    }

    /**
     * 核验请求是否合法
     *
     * @param head
     */
    private void checkAccessToken(ApiRequestHead head) {
        if (head == null
                || head.getAccessToken() == null
                || head.getAccountId() == null) {
            throw new ServiceException("AccessToken为空");
        }
        String accessToken = CacheUtils.get("mzt_token", head.getAccountId());
        if (!head.getAccessToken().equals(accessToken)) {
            throw new ServiceException("Token已失效或者无效");
        }
    }

    /**
     * 解析获取用户信息
     *
     * @param data
     * @return
     */
    private String getSessionByPhone(Map data) {
        Object object = data.get("sjh");
        return getSessionByPhone(object.toString());
    }

    private String getSessionByPhone(String phone) {
        if (phone == null) {
            throw new ServiceException("没有用户身份信息:sjh");
        }
        // 获取手机号对应的锁
        Object lock = phoneLocks.computeIfAbsent(phone, k -> new Object());

        synchronized (lock) {
            // double-check：加锁后再次判断
            String sessionId = this.checkSessionId(phone);
            if (!StringUtils.hasText(sessionId)) {
                logger.debug("当前用户没有会话记录，sjh：" + phone);
                sessionId = this.getRequestSession(phone);
            }

            // 若系统稳定运行一段时间后，可尝试清理锁对象（非强制）
//            phoneLocks.remove(phone);
            return sessionId;
        }
    }


    private String getRequestSession(String phone) {
        // 获取手机号对应的锁
        Object lock = phoneLocks.computeIfAbsent(phone, k -> new Object());
        synchronized (lock) {
            EmpUser user = this.getUserByPhone(phone);
            String token = UserUtils.getSsoToken(user.getLoginCode());
            JSONObject result = restTemplate.getForObject(
                    "http://127.0.0.1:" + Global.getProperty("server.port") + FileUtils.path("/"
                            + Global.getProperty("server.servlet.context-path")) + "/ssoJson/" + user.getLoginCode() + "/" + token,
                    JSONObject.class);
            String sessionId = result.getString("session");
            Session session = sessionDAO.readSession(sessionId);
            session.setAttribute("phone", phone);
            CacheUtils.put("mzt_session", phone, sessionId,
                    Long.parseLong(Global.getConfig("expiresIn", "3600")));
            logger.debug("用户会话记录缓存，sjh：" + phone + "，mzt_session：" + sessionId);
            return sessionId;
        }
    }

    /**
     * 验证用户是否还在登录
     *
     * @param phone
     * @return
     */
    private String checkSessionId(String phone) {
        String sessionId = CacheUtils.get("mzt_session", phone);
        if (StringUtils.isEmpty(sessionId)) {
            return null;
        }
        Session session = sessionDAO.readSession(sessionId);
        if (session == null
                || new Date().after(DateUtils.addMilliseconds(session.getStartTimestamp(), Math.toIntExact(session.getTimeout())))
                || !session.getAttribute("phone").equals(phone)) {
            CacheUtils.remove("mzt_session", phone);
            return null;
        }
        return sessionId;
    }


    /**
     * 验证Session中的用户信息是否与手机号匹配
     * @param session
     * @param phone
     * @return
     */
    private boolean validateSessionUserMatch(Session session, String phone) {
        try {
            // 从Session中获取用户登录信息
            Object pc = session.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY);
            if (pc != null && pc instanceof org.apache.shiro.subject.PrincipalCollection) {
                Object pp = ((org.apache.shiro.subject.PrincipalCollection)pc).getPrimaryPrincipal();
                if (pp != null && pp instanceof com.jeesite.common.shiro.realm.LoginInfo) {
                    com.jeesite.common.shiro.realm.LoginInfo loginInfo = (com.jeesite.common.shiro.realm.LoginInfo)pp;
                    String userId = loginInfo.getId();

                    // 根据userId获取用户信息，验证手机号是否匹配
                    EmpUser sessionUser = empUserService.get(userId);
                    if (sessionUser != null && phone.equals(sessionUser.getMobile())) {
                        return true;
                    }

                    logger.debug("Session用户手机号不匹配，sessionUserId: {}, sessionUserMobile: {}, requestPhone: {}",
                            userId, sessionUser != null ? sessionUser.getMobile() : "null", phone);
                }
            }
        } catch (Exception e) {
            logger.error("验证Session用户匹配时发生异常，phone: {}, sessionId: {}", phone, session.getId(), e);
        }
        return false;
    }

    private EmpUser getUserByPhone(String phone) {
        EmpUser query = new EmpUser();
        query.setMobile(phone);
        return empUserService.findList(query).stream().findFirst().orElseThrow(() -> new ServiceException("手机号" + phone + "用户不存在！"));
    }

    /**
     * 文件上传接口
     *
     * @param body
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "api/fileUpload")
    public ApiResponseBody fileUpload(@RequestBody ApiRequestBody<ApiFileUploadRequest> body) throws Exception {
        //核验用户信息
        this.checkAccessToken(body.getHead());

        ApiFileUploadRequest apiFileUpload = body.getData();

        // Step 1: 解码 Base64 文件
        byte[] fileBytes = Base64.getDecoder().decode(apiFileUpload.getFileContent());

        // Step 2: 计算 MD5 值
        String md5 = DigestUtils.md5Hex(fileBytes);

        FileUploadParams fileParams = new FileUploadParams();
        MultipartFile multipartFile = new MockMultipartFile(
                apiFileUpload.getFileName(),
                apiFileUpload.getFileOriginName(),
                "application/octet-stream",
                fileBytes
        );
        fileParams.setBizKey(apiFileUpload.getFileName());
        fileParams.setFile(multipartFile);
        fileParams.setFileMd5(md5);
        fileParams.setFileName(apiFileUpload.getFileOriginName());
        FileUpload fileUpload = new FileUpload();
        Map m = fileUploadService.uploadFile(fileUpload, fileParams);
        return this.getFileUploadResult(m);
    }

    /**
     * 6.3.5用户验证接口6.3.5用户验证接口
     *
     * @param body
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "api/hs/checkUser")
    public ApiResponseBody checkUser(@RequestBody ApiRequestBody<ApiUserInfo> body) throws Exception {
        Map result = new HashMap();
        //核验用户信息
        this.checkAccessToken(body.getHead());
        //获取用户信息
        try {
            EmpUser query = new EmpUser();
            query.setMobile(body.getData().getSjh());
            EmpUser user = empUserService.findList(query).stream().findFirst().orElse(null);
            result.put("isUser", user != null ? "0" : "1");
            if (user != null) {
                Employee employee = employeeService.get(user.getId());
                result.put("officeCode", employee.getOffice().getOfficeCode());
                result.put("officeName", employee.getOffice().getOfficeName());
            }
            return ApiResponseBody.sucess(result);
        } catch (ServiceException e) {
            return ApiResponseBody.error("用户信息查询失败！");
        }
    }

    private ApiResponseBody getFileUploadResult(Map m) {
        Map result = new HashMap();
        if (m == null || m.get("result") == null) {
            return ApiResponseBody.error("文件上传失败");
        }
        if (m.get("result").toString().equals("false")) {
            return ApiResponseBody.error("文件上传失败！" + m.get("message"));
        }
        FileUpload fileUpload = (FileUpload) m.get("fileUpload");
        result.put("fileId", fileUpload.getId());
        return ApiResponseBody.sucess(result);
    }

    /**
     * 文件下载接口
     *
     * @param body
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "api/fileDownload")
    public ApiResponseBody fileDownload(@RequestBody ApiRequestBody<ApiFileDownloadRequest> body) throws Exception {
        //核验用户信息
        this.checkAccessToken(body.getHead());
        ApiFileDownloadRequest apiFileDownloadRequest = body.getData();
        FileUpload fileUpload = fileUploadService.getFile(new FileUpload(apiFileDownloadRequest.getFileId()));
        if (fileUpload == null) {
            return ApiResponseBody.error("文件下载失败");
        }
        return ApiResponseBody.sucess(this.getFileBase64(fileUpload));
    }


    /**
     * 文件转换成base64
     *
     * @param fileUpload
     * @return
     * @throws IOException
     */
    private ApiFileDownloadResponse getFileBase64(FileUpload fileUpload) throws IOException {
        ApiFileDownloadResponse result = new ApiFileDownloadResponse();
        // 获取文件输入流（可以是文件、数据库等）
        InputStream fileInputStream = Files.newInputStream(new File(fileUpload.getFileEntity().getFileRealPath()).toPath());  // 假设这个方法返回一个文件的 InputStream

        // Step 1: 使用 ByteArrayOutputStream 存储字节数据
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        byte[] buffer = new byte[8192];  // 设定缓冲区大小
        int length;

        // Step 2: 读取文件流并写入 ByteArrayOutputStream
        while ((length = fileInputStream.read(buffer)) != -1) {
            byteArrayOutputStream.write(buffer, 0, length);
        }

        // Step 3: 获取文件字节数据
        byte[] fileBytes = byteArrayOutputStream.toByteArray();

        // Step 4: 将字节数据转换为 Base64 编码的字符串
        String fileContent = Base64.getEncoder().encodeToString(fileBytes);
        result.setFileContent(fileContent);
        result.setFileName(fileUpload.getFileName());
        return result;
    }

}
