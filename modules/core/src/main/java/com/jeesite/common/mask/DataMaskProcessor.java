package com.jeesite.common.mask;

import com.jeesite.common.annotation.DataMask;
import com.jeesite.common.config.Global;
import com.jeesite.common.lang.StringUtils;
import com.jeesite.common.utils.DataMaskUtils;
import com.jeesite.modules.sys.utils.UserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

/**
 * 数据脱敏处理器
 * 
 * <AUTHOR>
 * @version 2024-01-01
 */
public class DataMaskProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(DataMaskProcessor.class);
    
    private static final ExpressionParser parser = new SpelExpressionParser();
    
    /**
     * 处理单个对象的脱敏
     * 
     * @param obj 待处理的对象
     * @return 处理后的对象
     */
    public static <T> T process(T obj) {
        if (obj == null) {
            return obj;
        }
        
        // 检查全局脱敏开关
        if (!isGlobalMaskEnabled()) {
            return obj;
        }
        
        try {
            processObject(obj);
        } catch (Exception e) {
            logger.error("数据脱敏处理失败", e);
        }
        
        return obj;
    }
    
    /**
     * 处理集合对象的脱敏
     * 
     * @param list 待处理的集合
     * @return 处理后的集合
     */
    public static <T> List<T> processList(List<T> list) {
        if (list == null || list.isEmpty()) {
            return list;
        }
        
        if (!isGlobalMaskEnabled()) {
            return list;
        }
        
        for (T item : list) {
            process(item);
        }
        
        return list;
    }
    
    /**
     * 处理对象的脱敏
     */
    private static void processObject(Object obj) throws Exception {
        if (obj == null) {
            return;
        }
        
        Class<?> clazz = obj.getClass();
        
        // 处理基本类型和包装类型
        if (isBasicType(clazz)) {
            return;
        }
        
        // 处理集合类型
        if (obj instanceof Collection) {
            Collection<?> collection = (Collection<?>) obj;
            for (Object item : collection) {
                processObject(item);
            }
            return;
        }
        
        // 处理Map类型
        if (obj instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) obj;
            for (Object value : map.values()) {
                processObject(value);
            }
            return;
        }
        
        // 处理字段
        processFields(obj, clazz);
        
        // 处理方法
        processMethods(obj, clazz);
    }
    
    /**
     * 处理字段脱敏
     */
    private static void processFields(Object obj, Class<?> clazz) throws Exception {
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            DataMask dataMask = field.getAnnotation(DataMask.class);
            if (dataMask == null) {
                continue;
            }
            
            // 检查脱敏条件
            if (!shouldMask(dataMask, obj)) {
                continue;
            }
            
            field.setAccessible(true);
            Object value = field.get(obj);
            
            if (value instanceof String) {
                String maskedValue = maskValue((String) value, dataMask);
                field.set(obj, maskedValue);
            } else if (value != null) {
                // 递归处理嵌套对象
                processObject(value);
            }
        }
        
        // 处理父类字段
        Class<?> superClass = clazz.getSuperclass();
        if (superClass != null && !superClass.equals(Object.class)) {
            processFields(obj, superClass);
        }
    }
    
    /**
     * 处理方法脱敏（主要用于getter方法）
     */
    private static void processMethods(Object obj, Class<?> clazz) throws Exception {
        Method[] methods = clazz.getDeclaredMethods();
        
        for (Method method : methods) {
            DataMask dataMask = method.getAnnotation(DataMask.class);
            if (dataMask == null) {
                continue;
            }
            
            // 只处理getter方法
            if (!isGetterMethod(method)) {
                continue;
            }
            
            // 检查脱敏条件
            if (!shouldMask(dataMask, obj)) {
                continue;
            }
            
            // 查找对应的setter方法
            String setterName = method.getName().replaceFirst("get", "set");
            Method setter = null;
            try {
                setter = clazz.getMethod(setterName, method.getReturnType());
            } catch (NoSuchMethodException e) {
                // 没有setter方法，跳过
                continue;
            }
            
            method.setAccessible(true);
            Object value = method.invoke(obj);
            
            if (value instanceof String) {
                String maskedValue = maskValue((String) value, dataMask);
                setter.setAccessible(true);
                setter.invoke(obj, maskedValue);
            }
        }
    }
    
    /**
     * 执行脱敏处理
     */
    private static String maskValue(String value, DataMask dataMask) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        
        if (dataMask.type() == DataMask.MaskType.CUSTOM) {
            return DataMaskUtils.maskData(value, dataMask.pattern(), dataMask.replacement());
        } else {
            return DataMaskUtils.maskData(value, dataMask.type());
        }
    }
    
    /**
     * 判断是否应该执行脱敏
     */
    private static boolean shouldMask(DataMask dataMask, Object obj) {
        // 检查注解是否启用
        if (!dataMask.enabled()) {
            return false;
        }
        
        // 检查脱敏条件（SpEL表达式）
        if (StringUtils.isNotBlank(dataMask.condition())) {
            try {
                Expression expression = parser.parseExpression(dataMask.condition());
                EvaluationContext context = new StandardEvaluationContext(obj);
                
                // 添加一些常用的上下文变量
                context.setVariable("currentUser", UserUtils.getUser());
                context.setVariable("hasPermission", new PermissionHelper());
                
                Boolean result = expression.getValue(context, Boolean.class);
                return result != null && result;
            } catch (Exception e) {
                logger.warn("脱敏条件表达式执行失败：{}", dataMask.condition(), e);
                return true; // 默认执行脱敏
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否为getter方法
     */
    private static boolean isGetterMethod(Method method) {
        String methodName = method.getName();
        return methodName.startsWith("get") && 
               methodName.length() > 3 && 
               method.getParameterCount() == 0 && 
               !method.getReturnType().equals(void.class);
    }
    
    /**
     * 检查是否为基本类型
     */
    private static boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() || 
               clazz.equals(String.class) ||
               clazz.equals(Integer.class) ||
               clazz.equals(Long.class) ||
               clazz.equals(Double.class) ||
               clazz.equals(Float.class) ||
               clazz.equals(Boolean.class) ||
               clazz.equals(Character.class) ||
               clazz.equals(Byte.class) ||
               clazz.equals(Short.class) ||
               Number.class.isAssignableFrom(clazz) ||
               Date.class.isAssignableFrom(clazz);
    }
    
    /**
     * 检查全局脱敏开关
     */
    private static boolean isGlobalMaskEnabled() {
        // 从配置文件读取全局脱敏开关
        boolean globalEnabled = Global.getConfigToBoolean("dataMask.enabled", "false");
        
        // 检查用户权限
        if (globalEnabled) {
            try {
                // 如果用户有查看敏感数据的权限，则不脱敏
                return !UserUtils.getSubject().isPermitted("sys:data:viewSensitive");
            } catch (Exception e) {
                // 如果获取权限失败，默认脱敏
                return true;
            }
        }
        
        return globalEnabled;
    }
    
    /**
     * 权限辅助类，用于SpEL表达式
     */
    public static class PermissionHelper {
        
        public boolean hasPermission(String permission) {
            try {
                return UserUtils.getSubject().isPermitted(permission);
            } catch (Exception e) {
                return false;
            }
        }
        
        public boolean hasRole(String role) {
            try {
                return UserUtils.getSubject().hasRole(role);
            } catch (Exception e) {
                return false;
            }
        }
    }
}
