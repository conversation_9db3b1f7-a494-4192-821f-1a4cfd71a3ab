package com.jeesite.common.mask;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jeesite.common.entity.Page;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Collection;
import java.util.Map;

/**
 * 数据脱敏响应拦截器
 * 自动对Controller返回的数据进行脱敏处理
 * 
 * <AUTHOR>
 * @version 2024-01-01
 */
@ControllerAdvice
public class DataMaskInterceptor implements ResponseBodyAdvice<Object> {
    
    private static final Logger logger = LoggerFactory.getLogger(DataMaskInterceptor.class);
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 只处理JSON响应
        logger.info("DataMaskInterceptor.supports() 被调用，returnType: {}, converterType: {}",
                   returnType.getParameterType().getSimpleName(),
                   converterType.getSimpleName());
        return true;
    }
    
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                  Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  ServerHttpRequest request, ServerHttpResponse response) {

        logger.info("DataMaskInterceptor.beforeBodyWrite() 被调用，URI: {}, ContentType: {}",
                   request.getURI().getPath(), selectedContentType);

        // 只处理JSON响应
        if (selectedContentType == null || !selectedContentType.includes(MediaType.APPLICATION_JSON)) {
            logger.info("非JSON响应，跳过脱敏处理");
            return body;
        }
        
        // 检查是否需要脱敏
        if (!shouldProcessMask(request)) {
            return body;
        }
        
        try {
            logger.debug("开始处理响应数据脱敏，URI: {}", request.getURI().getPath());
            return processResponseBody(body);
        } catch (Exception e) {
            logger.error("响应数据脱敏处理失败", e);
            return body;
        }
    }
    
    /**
     * 处理响应体数据
     */
    private Object processResponseBody(Object body) {
        if (body == null) {
            return body;
        }
        
        // 处理集合类型
        if (body instanceof Collection) {
            Collection<?> collection = (Collection<?>) body;
            return DataMaskProcessor.processList((java.util.List<?>) collection);
        }

        // 处理集合类型
        if (body instanceof Page) {
            Page<?> page = (Page<?>) body;
            return DataMaskProcessor.processList(page.getList());
        }
        
        // 处理Map类型（通常是分页数据或包装的响应）
        if (body instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) body;
            processMapValues(map);
            return body;
        }
        
        // 处理单个对象
        return DataMaskProcessor.process(body);
    }
    
    /**
     * 处理Map中的值
     */
    private void processMapValues(Map<?, ?> map) {
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            Object value = entry.getValue();
            
            if (value instanceof Collection) {
                DataMaskProcessor.processList((java.util.List<?>) value);
            } else if (value instanceof Map) {
                processMapValues((Map<?, ?>) value);
            } else if (value != null) {
                // 对于Map中的单个对象，需要特殊处理
                Object processedValue = DataMaskProcessor.process(value);
                if (map instanceof java.util.HashMap) {
                    ((java.util.HashMap<Object, Object>) map).put(entry.getKey(), processedValue);
                }
            }
        }
    }
    
    /**
     * 判断是否需要处理脱敏
     */
    private boolean shouldProcessMask(ServerHttpRequest request) {
        String uri = request.getURI().getPath();
        
        // 排除不需要脱敏的接口
        String[] excludePaths = {
            "/api/login",
            "/api/logout", 
            "/api/captcha",
            "/static/",
            "/assets/",
            "/js/",
            "/css/",
            "/images/"
        };
        
        for (String excludePath : excludePaths) {
            if (uri.contains(excludePath)) {
                logger.debug("URI {} 在排除列表中，跳过脱敏处理", uri);
                return false;
            }
        }
        
        // 检查请求头中的脱敏标识
        String maskHeader = request.getHeaders().getFirst("X-Data-Mask");
        if ("false".equalsIgnoreCase(maskHeader)) {
            logger.debug("请求头 X-Data-Mask=false，跳过脱敏处理");
            return false;
        }
        
        logger.debug("URI {} 需要进行脱敏处理", uri);
        return true;
    }
}
