package com.jeesite.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据脱敏注解
 * 用于标记需要脱敏的字段
 * 
 * <AUTHOR>
 * @version 2024-01-01
 */
@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DataMask {
    
    /**
     * 脱敏类型
     */
    MaskType type() default MaskType.CUSTOM;
    
    /**
     * 自定义脱敏规则（当type为CUSTOM时使用）
     * 支持正则表达式替换，如：$1****$2
     */
    String pattern() default "";
    
    /**
     * 自定义替换规则
     */
    String replacement() default "";
    
    /**
     * 是否启用脱敏（可以通过配置动态控制）
     */
    boolean enabled() default true;
    
    /**
     * 脱敏条件（SpEL表达式）
     * 例如：@hasPermission('VIEW_SENSITIVE_DATA')
     */
    String condition() default "";
    
    /**
     * 脱敏类型枚举
     */
    enum MaskType {
        /**
         * 姓名脱敏：保留姓氏，名字用*替换
         */
        NAME,
        
        /**
         * 手机号脱敏：保留前3位和后4位
         */
        MOBILE,
        
        /**
         * 身份证脱敏：保留前6位和后4位
         */
        ID_CARD,
        
        /**
         * 银行卡脱敏：保留前4位和后4位
         */
        BANK_CARD,
        
        /**
         * 邮箱脱敏：保留前3位和@后域名
         */
        EMAIL,
        
        /**
         * 地址脱敏：保留前6位，后面用*替换
         */
        ADDRESS,
        
        /**
         * 默认脱敏：保留前后各1位，中间用*替换
         */
        DEFAULT,
        
        /**
         * 自定义脱敏：使用pattern和replacement
         */
        CUSTOM
    }
}
