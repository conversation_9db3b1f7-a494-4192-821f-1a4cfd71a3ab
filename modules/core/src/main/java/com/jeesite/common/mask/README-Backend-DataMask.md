# 后台数据脱敏使用说明

## 概述

后台数据脱敏系统提供了完整的服务端数据脱敏解决方案，支持注解驱动、自动拦截、权限控制等功能。

## 核心组件

### 1. 注解系统
- `@DataMask` - 数据脱敏注解
- 支持多种脱敏类型：姓名、手机号、身份证、银行卡、邮箱、地址等
- 支持自定义脱敏规则
- 支持条件脱敏（SpEL表达式）

### 2. 工具类
- `DataMaskUtils` - 脱敏工具类，提供各种脱敏方法
- `DataMaskProcessor` - 脱敏处理器，处理对象和集合的脱敏

### 3. 拦截器
- `DataMaskInterceptor` - 响应拦截器，自动对Controller返回数据进行脱敏

## 使用方法

### 1. 实体类注解

```java
@Entity
public class User {
    
    @DataMask(type = DataMask.MaskType.NAME)
    private String userName;
    
    @DataMask(type = DataMask.MaskType.MOBILE)
    private String mobile;
    
    @DataMask(
        type = DataMask.MaskType.ID_CARD,
        condition = "!@hasPermission.hasPermission('user:viewIdCard')"
    )
    private String idCard;
    
    @DataMask(
        type = DataMask.MaskType.CUSTOM,
        pattern = "(\\d{4})\\d+(\\d{4})",
        replacement = "$1****$2"
    )
    private String bankCard;
    
    // getter/setter...
}
```

### 2. Service层使用

```java
@Service
public class UserService {
    
    // 自动脱敏
    public User getUser(String id) {
        User user = userDao.findById(id);
        return DataMaskProcessor.process(user);
    }
    
    // 不脱敏
    public User getUserRaw(String id) {
        return userDao.findById(id);
    }
    
    // 批量脱敏
    public List<User> getUserList() {
        List<User> users = userDao.findAll();
        return DataMaskProcessor.processList(users);
    }
}
```

### 3. Controller层使用

```java
@Controller
public class UserController {
    
    // 自动脱敏（通过拦截器）
    @RequestMapping("/api/users")
    @ResponseBody
    public List<User> getUsers() {
        return userService.findAll();
    }
    
    // 不脱敏（设置请求头）
    @RequestMapping(value = "/api/users/raw", headers = "X-Data-Mask=false")
    @ResponseBody
    public List<User> getUsersRaw() {
        return userService.findAllRaw();
    }
    
    // 手动脱敏
    @RequestMapping("/api/users/manual")
    @ResponseBody
    public User getUserManual(@RequestParam String id) {
        User user = userService.getUserRaw(id);
        return DataMaskProcessor.process(user);
    }
}
```

## 脱敏类型

### 支持的脱敏类型

| 类型 | 枚举值 | 脱敏规则 | 示例 |
|------|--------|----------|------|
| 姓名 | NAME | 保留姓氏，名字用*替换 | 张三 → 张* |
| 手机号 | MOBILE | 保留前3位和后4位 | *********** → 138****5678 |
| 身份证 | ID_CARD | 保留前6位和后4位 | 110101199001011234 → 110101********1234 |
| 银行卡 | BANK_CARD | 保留前4位和后4位 | **************** → 6222********7890 |
| 邮箱 | EMAIL | 保留前3位和@后域名 | <EMAIL> → tes***@example.com |
| 地址 | ADDRESS | 保留前6位，后面用*替换 | 北京市朝阳区建国门外大街1号 → 北京市朝阳区****** |
| 默认 | DEFAULT | 保留前后各1位，中间用*替换 | 12345 → 1***5 |
| 自定义 | CUSTOM | 使用自定义正则表达式 | 自定义规则 |

## 权限控制

### 1. 基于权限的脱敏

```java
@DataMask(
    type = DataMask.MaskType.ID_CARD,
    condition = "!@hasPermission.hasPermission('user:viewIdCard')"
)
private String idCard;
```

### 2. 基于角色的脱敏

```java
@DataMask(
    type = DataMask.MaskType.MOBILE,
    condition = "!@hasPermission.hasRole('admin')"
)
private String mobile;
```

### 3. 复杂条件脱敏

```java
@DataMask(
    type = DataMask.MaskType.NAME,
    condition = "#currentUser.deptId != this.deptId && !@hasPermission.hasPermission('user:viewAllNames')"
)
private String userName;
```

## 配置说明

### 1. 全局配置

在 `application.yml` 中添加：

```yaml
dataMask:
  enabled: true  # 全局脱敏开关
  
  permissions:
    viewSensitive: "sys:data:viewSensitive"
    viewIdCard: "user:viewIdCard"
    viewMobile: "user:viewMobile"
```

### 2. 权限配置

在权限系统中添加相应权限：
- `sys:data:viewSensitive` - 查看敏感数据权限
- `user:viewIdCard` - 查看身份证权限
- `user:viewMobile` - 查看手机号权限

## 高级功能

### 1. 自定义脱敏规则

```java
@DataMask(
    type = DataMask.MaskType.CUSTOM,
    pattern = "^(.{2}).*(.{2})$",
    replacement = "$1****$2"
)
private String customField;
```

### 2. 条件脱敏

```java
@DataMask(
    type = DataMask.MaskType.NAME,
    condition = "#currentUser.id != this.id"  // 只对其他用户脱敏
)
private String userName;
```

### 3. 方法级脱敏

```java
public class User {
    
    @DataMask(type = DataMask.MaskType.MOBILE)
    public String getMobile() {
        return this.mobile;
    }
}
```

## 性能优化

### 1. 批量处理

```java
// 推荐：批量处理
List<User> users = userService.findAll();
DataMaskProcessor.processList(users);

// 不推荐：逐个处理
for (User user : users) {
    DataMaskProcessor.process(user);
}
```

### 2. 缓存优化

```java
// 在Service层缓存脱敏结果
@Cacheable(value = "maskedUsers", key = "#id")
public User getMaskedUser(String id) {
    User user = userDao.findById(id);
    return DataMaskProcessor.process(user);
}
```

## 注意事项

1. **性能影响**：脱敏处理会增加一定的性能开销，建议在必要时使用
2. **数据完整性**：脱敏只影响展示，不影响数据存储
3. **权限控制**：确保权限配置正确，避免敏感数据泄露
4. **测试验证**：充分测试各种脱敏场景，确保功能正常

## 常见问题

### Q: 如何禁用某个字段的脱敏？
A: 设置 `enabled = false` 或不添加 `@DataMask` 注解

### Q: 如何根据用户权限动态脱敏？
A: 使用 `condition` 属性配合SpEL表达式

### Q: 如何自定义脱敏规则？
A: 使用 `CUSTOM` 类型，配置 `pattern` 和 `replacement`

### Q: 脱敏是否影响数据库存储？
A: 不影响，脱敏只在数据展示时进行处理
