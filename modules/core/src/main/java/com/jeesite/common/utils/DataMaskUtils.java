package com.jeesite.common.utils;

import com.jeesite.common.annotation.DataMask;
import com.jeesite.common.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 数据脱敏工具类
 * 
 * <AUTHOR>
 * @version 2024-01-01
 */
public class DataMaskUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(DataMaskUtils.class);
    
    /**
     * 根据脱敏类型对数据进行脱敏处理
     * 
     * @param value 原始值
     * @param maskType 脱敏类型
     * @return 脱敏后的值
     */
    public static String maskData(String value, DataMask.MaskType maskType) {
        if (StringUtils.isBlank(value)) {
            return value;
        }
        
        try {
            switch (maskType) {
                case NAME:
                    return maskName(value);
                case MOBILE:
                    return maskMobile(value);
                case ID_CARD:
                    return maskIdCard(value);
                case BANK_CARD:
                    return maskBankCard(value);
                case EMAIL:
                    return maskEmail(value);
                case ADDRESS:
                    return maskAddress(value);
                case DEFAULT:
                    return maskDefault(value);
                default:
                    return value;
            }
        } catch (Exception e) {
            logger.warn("数据脱敏处理失败，原值：{}，类型：{}", value, maskType, e);
            return value;
        }
    }
    
    /**
     * 自定义脱敏处理
     * 
     * @param value 原始值
     * @param pattern 正则表达式
     * @param replacement 替换规则
     * @return 脱敏后的值
     */
    public static String maskData(String value, String pattern, String replacement) {
        if (StringUtils.isBlank(value) || StringUtils.isBlank(pattern)) {
            return value;
        }
        
        try {
            return value.replaceAll(pattern, replacement);
        } catch (Exception e) {
            logger.warn("自定义脱敏处理失败，原值：{}，模式：{}，替换：{}", value, pattern, replacement, e);
            return value;
        }
    }
    
    /**
     * 姓名脱敏：保留姓氏，名字用*替换
     * 张三 -> 张*
     * 欧阳修 -> 欧**
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name) || name.length() <= 1) {
            return name;
        }
        
        if (name.length() == 2) {
            return name.charAt(0) + "*";
        }
        
        // 处理复姓情况
        String surname = name.substring(0, 1);
        if (name.length() >= 3 && isCompoundSurname(name.substring(0, 2))) {
            surname = name.substring(0, 2);
        }
        
        int maskLength = name.length() - surname.length();
        return surname + StringUtils.repeat("*", maskLength);
    }
    
    /**
     * 手机号脱敏：保留前3位和后4位
     * 13812345678 -> 138****5678
     */
    public static String maskMobile(String mobile) {
        if (StringUtils.isBlank(mobile) || mobile.length() != 11) {
            return mobile;
        }
        
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
    
    /**
     * 身份证脱敏：保留前6位和后4位
     * 110101199001011234 -> 110101********1234
     */
    public static String maskIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        
        if (idCard.length() == 15) {
            return idCard.replaceAll("(\\d{6})\\d{5}(\\d{4})", "$1*****$2");
        } else if (idCard.length() == 18) {
            return idCard.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2");
        }
        
        return idCard;
    }
    
    /**
     * 银行卡脱敏：保留前4位和后4位
     * **************** -> 6222********7890
     */
    public static String maskBankCard(String bankCard) {
        if (StringUtils.isBlank(bankCard) || bankCard.length() < 8) {
            return bankCard;
        }
        
        return bankCard.replaceAll("(\\d{4})\\d+(\\d{4})", "$1****$2");
    }
    
    /**
     * 邮箱脱敏：保留前3位和@后域名
     * <EMAIL> -> tes***@example.com
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email) || !email.contains("@")) {
            return email;
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email;
        }
        
        String username = parts[0];
        String domain = parts[1];
        
        if (username.length() <= 3) {
            return username + "***@" + domain;
        }
        
        return username.substring(0, 3) + "***@" + domain;
    }
    
    /**
     * 地址脱敏：保留前6位，后面用*替换
     * 北京市朝阳区建国门外大街1号 -> 北京市朝阳区******
     */
    public static String maskAddress(String address) {
        if (StringUtils.isBlank(address) || address.length() <= 6) {
            return address;
        }
        
        String prefix = address.substring(0, 6);
        int maskLength = Math.min(address.length() - 6, 6);
        return prefix + StringUtils.repeat("*", maskLength);
    }
    
    /**
     * 默认脱敏：保留前后各1位，中间用*替换
     * 12345 -> 1***5
     */
    public static String maskDefault(String value) {
        if (StringUtils.isBlank(value) || value.length() <= 2) {
            return value;
        }
        
        if (value.length() == 3) {
            return value.charAt(0) + "*" + value.charAt(2);
        }
        
        int maskLength = value.length() - 2;
        return value.charAt(0) + StringUtils.repeat("*", maskLength) + value.charAt(value.length() - 1);
    }
    
    /**
     * 判断是否为复姓
     */
    private static boolean isCompoundSurname(String surname) {
        // 常见复姓列表（可以根据需要扩展）
        String[] compoundSurnames = {
            "欧阳", "太史", "端木", "上官", "司马", "东方", "独孤", "南宫", "万俟", "闻人",
            "夏侯", "诸葛", "尉迟", "公羊", "赫连", "澹台", "皇甫", "宗政", "濮阳", "公冶",
            "太叔", "申屠", "公孙", "慕容", "仲孙", "钟离", "长孙", "宇文", "司徒", "鲜于"
        };
        
        for (String compoundSurname : compoundSurnames) {
            if (compoundSurname.equals(surname)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查字符串是否匹配手机号格式
     */
    public static boolean isMobileNumber(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        
        String regex = "^1[3-9]\\d{9}$";
        return Pattern.matches(regex, str);
    }
    
    /**
     * 检查字符串是否匹配身份证格式
     */
    public static boolean isIdCard(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        
        String regex15 = "^\\d{15}$";
        String regex18 = "^\\d{17}[\\dXx]$";
        
        return Pattern.matches(regex15, str) || Pattern.matches(regex18, str);
    }
    
    /**
     * 检查字符串是否匹配邮箱格式
     */
    public static boolean isEmail(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }

        String regex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return Pattern.matches(regex, str);
    }
}
