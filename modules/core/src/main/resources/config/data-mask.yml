# 数据脱敏配置文件
dataMask:
  # 全局脱敏开关
  enabled: true
  
  # 脱敏规则配置
  rules:
    # 姓名脱敏规则
    name:
      enabled: true
      description: "姓名脱敏：保留姓氏，名字用*替换"
    
    # 手机号脱敏规则  
    mobile:
      enabled: true
      description: "手机号脱敏：保留前3位和后4位"
      
    # 身份证脱敏规则
    idCard:
      enabled: true
      description: "身份证脱敏：保留前6位和后4位"
      
    # 银行卡脱敏规则
    bankCard:
      enabled: true
      description: "银行卡脱敏：保留前4位和后4位"
      
    # 邮箱脱敏规则
    email:
      enabled: true
      description: "邮箱脱敏：保留前3位和@后域名"
      
    # 地址脱敏规则
    address:
      enabled: true
      description: "地址脱敏：保留前6位，后面用*替换"
  
  # 权限配置
  permissions:
    # 查看敏感数据权限
    viewSensitive: "sys:data:viewSensitive"
    
    # 查看身份证权限
    viewIdCard: "house:owner:viewIdCard"
    
    # 查看手机号权限
    viewMobile: "house:owner:viewMobile"
    
    # 查看银行卡权限
    viewBankCard: "house:owner:viewBankCard"
  
  # 角色配置
  roles:
    # 管理员角色（不脱敏）
    admin:
      enabled: false
      description: "管理员角色，不进行数据脱敏"
      
    # 普通用户角色（脱敏）
    user:
      enabled: true
      description: "普通用户角色，进行数据脱敏"
      
    # 查看者角色（脱敏）
    viewer:
      enabled: true
      description: "查看者角色，进行数据脱敏"
  
  # 页面级别配置
  pages:
    # 用户管理页面
    "/user/":
      enabled: true
      excludeFields: ["id", "createTime", "updateTime"]
      description: "用户管理页面脱敏配置"
      
    # 房屋管理页面
    "/house/":
      enabled: true
      excludeFields: ["houseId", "estateId"]
      description: "房屋管理页面脱敏配置"
      
    # 申请管理页面
    "/apply/":
      enabled: true
      excludeFields: []
      description: "申请管理页面脱敏配置"
  
  # 接口级别配置
  apis:
    # 排除的API路径（不进行脱敏）
    excludePaths:
      - "/api/login"
      - "/api/logout"
      - "/api/captcha"
      - "/static/"
      - "/assets/"
      - "/js/"
      - "/css/"
      - "/images/"
    
    # 需要脱敏的API路径
    includePaths:
      - "/api/house/"
      - "/api/user/"
      - "/api/apply/"
  
  # 日志配置
  logging:
    # 是否记录脱敏操作日志
    enabled: true
    
    # 日志级别
    level: "INFO"
    
    # 是否记录脱敏前后的数据对比
    logDataComparison: false
  
  # 性能配置
  performance:
    # 是否启用缓存
    cacheEnabled: true
    
    # 缓存过期时间（秒）
    cacheExpireTime: 3600
    
    # 批处理大小
    batchSize: 1000
